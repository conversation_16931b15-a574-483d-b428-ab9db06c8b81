<template>
  <AbsoluteContainer>
    <page-main>
      <div class="tab">
        <div
          v-for="(item, index) in tabData"
          :key="item.value"
          :class="step == index + 1 ? 'child act' : 'child'"
          @click="handleClickTab(item, index)"
        >
          {{ item.name }}
        </div>

        <div style="width: 100%">
          <div
            class="line"
            :style="'transform: translateX(' + (42 * (step - 1) + (step - 1) * 40) + 'px)'"
          ></div>
        </div>
        <div class="right">
        </div>
      </div>
      <el-divider />
      <Adopt v-if="activeName === 1" />
      <Examines v-if="activeName === 2" />
      <Delete v-if="activeName === 3" />
    </page-main>



  </AbsoluteContainer>
</template>
<script setup name="regularAudit">
import Adopt from './adopt/index.vue'
import Delete from './delete/index.vue'
import Examines from './examine/index.vue'
import { ref, onMounted } from 'vue'
import SettingIcon from '@/assets/images/settings_column_group.png'
import GroupIcon from '@/assets/images/settings_group.png'
import { useRouter } from 'vue-router'
const router = useRouter()
const store = useStore()
const activeName = ref(1)
const step = ref(1)
const tabData = ref([])
onMounted(() => {
  tabData.value = []
  let permissions = store.getters['menu/permissions']
  if (permissions.filter(v => v === 'comment:batch_action_audit').length) {
    tabData.value.push({
      name: '待审核',
      value: 1
    })
  }

  if (permissions.filter(v => v === 'comment:batch_action_delete').length) {
    tabData.value.push({
      name: '已通过',
      value: 2
    })
  }
  if (permissions.filter(v => v === 'comment:batch_action_cancel_to_pass').length) {
    tabData.value.push({
      name: '已删除',
      value: 3
    })
  }
  if (tabData.value.length) {
    activeName.value = tabData.value[0].value
  }
})
// 切换标签
function handleClickTab(item, val) {
  activeName.value = item.value
  step.value = val + 1
}

const groupDialogVisible = ref(false)
const onSettingsClick = () => {
  groupDialogVisible.value = true
}

const columnDialogVisible = ref(false)
const onColumnSettingClick = () => {
  columnDialogVisible.value = true
}

const closeColumnSetting = () => {
  columnDialogVisible.value = false
}

const onSettingComplete = () => {
  groupDialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.page-main {
  display: flex;
  flex-direction: column;
  .tab {
    height: 32px;
    display: flex;
    font-size: 14px;
    line-height: 32px;
    flex-wrap: wrap;
    position: relative;
    .child {
      cursor: pointer;
      margin-right: 40px;
    }
    .child.act {
      color: #0b82fd;
      font-weight: 600;
    }
    .line {
      flex-wrap: wrap;
      height: 4px;
      background: #0b82fd;
      width: 42px;
      transition: transform 0.2s linear;
      border-radius: 2px;
      position: relative;
      z-index: 10;
    }
    .right {
      position: absolute;
      right: 10px;
      bottom: 2px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 10px;
      .right-divider {
        width: 1px;
        height: 8px;
        background: #efefef;
      }
      .right-item {
        display: flex;
        align-items: center;

        .icon-setting {
          width: 13px;
          height: 13px;
          margin-right: 3px;
        }

        .icon-setting-column {
          width: 13px;
          height: 13px;
          margin-right: 3px;
        }
      }
    }
  }
  .el-divider--horizontal {
    margin-top: 2px;
  }
}
</style>
