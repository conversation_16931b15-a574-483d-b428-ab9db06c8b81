<template>
  <div class="head-box">
    <div class="left">
      <!-- <el-button
        v-auth="'comment:batch_action'" type="primary" :disabled="disabled==0"
        @click="emit('adoptClick')"
      >
        批量通过
      </el-button>
      <el-button
        v-auth="'comment:batch_action'" type="danger" :disabled="disabled==0"
        @click="emit('deleteClick')"
      >
        批量删除
      </el-button> -->
      <el-button
        v-auth="'comment:batch_action_audit'"
        color="#0B82FD"
        type="primary"
        :disabled="batchNumber.pass + batchNumber.del < 2"
        @click="emit('auditClick')"
      >
        批量审核
      </el-button>
      <span class="text">
        通过<span style="color: #0b82fd">{{ batchNumber.pass }}</span
        >条，删除<span style="color: #ff0000">{{ batchNumber.del }}</span
        >条
      </span>
    </div>
    <div class="right">
      <!-- 全部频道 筛选-->
      <!-- <el-select v-model="searchData.comment_source" filterable clearable @change="change">
                <el-option v-for="item in comment_source_type" :key="item.key" :label="item.label" :value="item.key" />
            </el-select> -->
      <el-date-picker
        v-model="time"
        style="width: 220px; margin-right: 10px"
        class="time"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        :editable="false"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="defaultTime"
        :disabled-date="disabledDate"
        @change="handleTimeChange"
      >
      </el-date-picker>
      <!-- 圈子 -->
      <el-cascader
        v-model="circleValue"
        :options="circleList"
        :show-all-levels="false"
        clearable
        collapse-tags
        :props="circleProps"
        placeholder="请选择圈子"
        style="margin-right: 10px; width: 130px"
        @change="changeCircle"
      />
      <!-- 频道 -->
      <el-cascader
        v-model="cascadeValue"
        :options="options"
        :show-all-levels="false"
        clearable
        collapse-tags
        :props="optionsProps"
        placeholder="全部频道"
        style="margin-right: 10px; width: 130px"
        @change="handleCategoryChange"
      />
      <!-- 评论类型 -->
      <el-select
        v-model="searchData.comment_type"
        style="width: 110px; margin-right: 10px"
        @change="handleSearch"
      >
        <el-option
          v-for="item of commentType"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        >
        </el-option>
      </el-select>
      <el-popover placement="bottom" :width="310" trigger="click">
        <template #reference>
          <div style="position: relative">
            <el-input v-model="wordCountDisplay" style="width: 120px" placeholder="评论字数筛选" readonly />
            <div style="position: absolute; top: 0; right: 0; width: 100%; height: 100%"></div>
          </div>
        </template>
        <el-input-number
          v-model="wordNumber.min"
          class="mx-4"
          :min="0"
          :max="wordNumber.max"
          controls-position="right"
          style="width: 80px"
        />
        -
        <el-input-number
          v-model="wordNumber.max"
          class="mx-4"
          :min="wordNumber.min"
          :max="250"
          controls-position="right"
          style="width: 80px"
        />
        <el-button color="#0B82FD" type="primary" size="small" text style="margin-left: 10px" @click="handleNumberConfirm">
          确定
        </el-button>
        <el-button
          color="#0B82FD"
          type="primary"
          size="small"
          text
          style="margin-left: 0"
          @click="handleNumberClear"
        >
          清空
        </el-button>
      </el-popover>

      <el-divider direction="vertical" />
      <!-- 搜索条件 -->
      <el-select
        v-model="searchData.comment_search_type"
        style="margin-right: 5px; width: 110px"
        @change="typeChange"
      >
        <el-option
          v-for="item of commentSearchType"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        >
        </el-option>
      </el-select>
      <!-- 除了 稿件标题-->
      <el-input
        v-if="searchData.comment_search_type != 'ARTICLE_TITLE'"
        v-model="searchData.searchword"
        style="width: 200px"
        placeholder="请输入关键词"
        class="input-with-select"
        clearable
        @keyup.enter="handleSearch"
      >
      </el-input>
      <!-- 稿件标题搜索的实时下拉 -->
      <el-select
        v-if="searchData.comment_search_type == 'ARTICLE_TITLE'"
        v-model="titleSearchData"
        style="width: 200px; margin-right: 0"
        filterable
        remote
        :remote-method="remoteMethod"
        :loading="loading"
        clearable
        placeholder="请输入稿件关键字"
        popper-class="selectElem"
        :teleported="false"
        @clear="handleClearTitle"
        @change="titleChange"
      >
        <el-option
          v-for="item in titleSelectData"
          :key="item.key"
          :label="item.label"
          :value="item.label"
        />
      </el-select>
      <!-- 搜索按钮 -->
      <el-button
        color="#0B82FD"
        type="primary"
        :icon="Search"
        style="margin-left: 5px"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <Output :type="1" :searchData="searchData" />
    </div>
  </div>
</template>
<script setup>
import { requests } from '@/api/business/auditManagement'
import { Search } from '@element-plus/icons'
import { ref, onMounted, defineEmits, defineExpose, computed } from 'vue'
import Output from '@/views/auditManagement/component/output.vue'
import { ElMessage } from 'element-plus'

const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]
const time = ref('')
// 组件接收事件
const emit = defineEmits(['search', 'adoptClick', 'deleteClick', 'auditClick'])
// 组件传参声明方式
const props = defineProps({
  disabled: {
    type: Number
  },
  id: {
    type: String
  }
})

// 频道相关
const cascadeValue = ref(null)
const options = ref([])
const optionsProps = ref({
  value: 'category_id',
  label: 'name',
  multiple: true
})

// 圈子相关
const circleList = ref([])
const circleValue = ref(null)
const circleProps = ref({
  value: 'id',
  label: 'name',
  multiple: true
})

// 稿件标题相关
const titleSelectData = ref([])
const titleSearchData = ref(null)
const loading = ref(false)

// 评论类型
const commentType = ref([
  { key: 'ALL', label: '全部评论' },
  { key: 'ARTICLE_TYPE', label: '稿件评论' },
  { key: 'REPLY_TYPE', label: '回复评论' }
])

// 字数筛选
const wordNumber = ref({
  min: 0,
  max: 250
})

// 搜索类型
const commentSearchType = ref([
  { key: 'CONTENT', label: '评论内容' },
  { key: 'ARTICLE_TITLE', label: '稿件标题' },
  { key: 'COMMENT_PERSON', label: '评论人' },
  { key: 'ARTICLE_ID', label: '稿件id' }
])

// 批量操作计数
const batchNumber = ref({
  pass: 0,
  del: 0
})

// 计算属性
const wordCountDisplay = computed(() => {
  if (searchData.value.word_number_min === '' && searchData.value.word_number_max === '') {
    return ''
  } else {
    return `${searchData.value.word_number_min}-${searchData.value.word_number_max}`
  }
})

// 搜索参数
const searchData = ref({
  state: 'PENDING', // 状态(PENDING-待审核，PASS-审核通过，UNPASS-已删除)
  time_type: 'COMMENT_DATE', // 评论日期类型（COMMENT_DATE：评论日期 AUDIT_DATE：审核日期.）
  start_time: '', // 起始时间
  end_time: '', // 结束时间
  category_id: '',
  comment_source: '', // 频道名称
  comment_type: 'ALL', // 筛选评论类型（ARTICLE_TYPE：稿件评论 REPLY_TYPE：回复评论）
  circle_id: '',
  word_number_min: '',
  word_number_max: '',
  comment_search_type: 'CONTENT', // 搜索类型
  searchword: '', // 关键词
  size: 100, // 每页条数
  current: 1 // 分页页码 默认1
})

onMounted(() => {
  // 获取分类树
  fetchCategoryTree()

  // 如果有ID参数，设置搜索类型为稿件ID
  if (props.id) {
    searchData.value.comment_search_type = 'ARTICLE_ID'
    searchData.value.searchword = props.id
  }

  // 获取圈子列表
  requests.getcircleList({}).then(res => {
    circleList.value = res.data.circle_list
  })

  // 初始搜索
  emit('search', searchData.value)
})

// 获取分类树
function fetchCategoryTree() {
  requests.listCategoryTree().then(res => {
    let arr = res.data.category_list
    // filterCategoryTree(arr)
    options.value = arr
  })
}

// 过滤分类树中没有权限的项
function filterCategoryTree(categories) {
  for (let i = 0; i < categories.length; i++) {
    if (parseInt(categories[i].permission) === 0) {
      categories.splice(i--, 1)
    } else {
      if (categories[i].children && categories[i].children.length > 0) {
        for (let k = 0; k < categories[i].children.length; k++) {
          if (categories[i].children[k].permission == 0) {
            categories[i].children.splice(k--, 1)
          } else if (categories[i].children[k].children && categories[i].children[k].children.length > 0) {
            for (let a = 0; a < categories[i].children[k].children.length; a++) {
              if (categories[i].children[k].children[a].permission == 0) {
                categories[i].children[k].children.splice(a--, 1)
              }
            }
          }
        }
      }
    }
  }
}

// 处理字数筛选确认
function handleNumberConfirm() {
  searchData.value.word_number_min = wordNumber.value.min
  searchData.value.word_number_max = wordNumber.value.max
  handleSearch()
}

// 清空字数筛选
function handleNumberClear() {
  searchData.value.word_number_min = ''
  searchData.value.word_number_max = ''
  wordNumber.value = { min: 0, max: 250 }
  handleSearch()
}

// 搜索类型改变
function typeChange() {
  titleSearchData.value = ''
  searchData.value.searchword = ''
}

// 远程搜索稿件标题
function remoteMethod(query) {
  titleSelectData.value = []
  if (!query) return

  loading.value = true
  const newSearchData = {
    ...searchData.value,
    size: 10,
    current: 1,
    searchword: query
  }

  requests.searchList(newSearchData).then(res => {
    loading.value = false
    titleSelectData.value = res.data.list.map(item => ({
      key: item.article_id,
      label: `【${item.article_id}】${item.article_title}`
    }))
  }).catch(() => {
    loading.value = false
  })
}

// 清空稿件标题
function handleClearTitle() {
  titleSearchData.value = null
}

// 禁用超过30天范围的日期
function disabledDate(time) {
  if (!time) return false

  // 获取当前选择的时间范围
  if (time.value && time.value.length > 0 && time.value[0]) {
    const startDate = new Date(time.value[0])
    const thirtyDaysLater = new Date(startDate)
    thirtyDaysLater.setDate(startDate.getDate() + 30)
    return time > thirtyDaysLater
  }

  return false
}

// 时间改变处理
function handleTimeChange() {
  if (time.value && time.value.length === 2) {
    const startTime = new Date(time.value[0])
    const endTime = new Date(time.value[1])

    // 结束日期不能早于开始日期
    if (endTime < startTime) {
      ElMessage.warning('结束时间不能早于开始时间')
      time.value = null
      return
    }

    // 检查时间范围是否超过30天
    const diffTime = endTime.getTime() - startTime.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays > 30) {
      // 如果超过30天，自动设置为开始日期后的第30天
      const maxEndTime = new Date(startTime)
      maxEndTime.setDate(startTime.getDate() + 30)
      maxEndTime.setHours(23, 59, 59)

      time.value = [time.value[0], maxEndTime.toISOString().slice(0, 19).replace('T', ' ')]
      ElMessage.warning('时间筛选范围已自动调整为30天')
    }
  }

  handleSearch()
}

// 搜索处理
function handleSearch() {
  // 处理时间
  if (time.value) {
    searchData.value.start_time = time.value[0]
    searchData.value.end_time = time.value[1]
  } else {
    searchData.value.start_time = ''
    searchData.value.end_time = ''
  }

  // 重置当前页
  searchData.value.current = 1

  // 判断搜索类型
  if (searchData.value.comment_search_type === 'ARTICLE_TITLE') {
    const newSearchData = { ...searchData.value }
    newSearchData.comment_search_type = 'ARTICLE_ID'

    if (titleSearchData.value) {
      const start = titleSearchData.value.indexOf('【') + 1
      const end = titleSearchData.value.indexOf('】')
      if (start > 0 && end > start) {
        newSearchData.searchword = titleSearchData.value.slice(start, end)
      }
    } else {
      newSearchData.searchword = ''
    }

    emit('search', newSearchData)
  } else {
    emit('search', searchData.value)
  }
}

// 频道改变处理
function handleCategoryChange() {
  if (cascadeValue.value) {
    let categoryIds = cascadeValue.value.join(',').split(',')
    categoryIds = Array.from(new Set(categoryIds))
    searchData.value.category_id = categoryIds.join(',')
  } else {
    searchData.value.category_id = ''
  }

  handleSearch()
}

// 圈子改变处理
function changeCircle() {
  if (circleValue.value) {
    searchData.value.circle_id = circleValue.value.join(',')
  } else {
    searchData.value.circle_id = ''
  }
  handleSearch()
}

// 稿件标题选择处理
function titleChange(val) {
  if (!val) return

  const newSearchData = { ...searchData.value }
  newSearchData.comment_search_type = 'ARTICLE_ID'

  const start = val.indexOf('【') + 1
  const end = val.indexOf('】')
  if (start > 0 && end > start) {
    newSearchData.searchword = val.slice(start, end)
    emit('search', newSearchData)
  }
}

// 对外暴露属性
defineExpose({
  searchData,
  title_search_data: titleSearchData,
  batchNumber
})
</script>
<style lang="scss" scoped>
.text {
  font-size: 14px;
  margin-left: 10px;
  color: #000;
}

.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;

  .left {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    white-space: nowrap;
    margin-right: 10px;
  }

  .right {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    row-gap: 6px;
  }
}

:deep(.selectElem) {
  width: 300px;

  .el-select-dropdown__item {
    white-space: pre-wrap;
    height: auto;
    line-height: 24px;
    padding: 5px 16px;

    &.hover,
    &:hover {
      background-color: #ebebeb;
    }
  }
}
</style>
<style>
.input-with-select .el-input-group__append {
  background-color: #1890ff;
  color: #fff;
}
</style>
