<template>
  <headModel
    :id="id"
    ref="searchBox"
    :disabled="selectedItemsCount"
    @search="search"
    @auditClick="auditClick"
    @adoptClick="adoptClick"
    @deleteClick="deleteClick"
  />
  <div id="tabel-component">
    <el-table v-loading="loading" :data="data.records" size="default" :height="data.height">
      <!-- <el-table-column type="selection"
                           width="40" /> -->
      <!-- <el-table-column prop="auto_pk"
            label="序号"
            width="150" /> -->
      <el-table-column v-auth="'comment:batch_action_audit'" width="70">
        <template #header>
          <el-checkbox
            v-model="passBut"
            class="checkBlue"
            label="通过"
            size="large"
            @change="handelPass"
          />
        </template>
        <template #default="scope">
          <div class="checkBlue">
            <el-checkbox
              v-model="scope.row.isPass"
              label=""
              size="large"
              fill="#0B82FD"
              @change="handelRowPass(scope.row.isPass, scope.row)"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column v-auth="'comment:batch_action_audit'" width="70">
        <template #header>
          <el-checkbox
            v-model="delBut"
            class="checkRed"
            label="删除"
            size="large"
            @change="handelDel"
          />
        </template>
        <template #default="scope">
          <div class="checkRed">
            <el-checkbox
              v-model="scope.row.isDel"
              label=""
              size="large"
              fill="#FF0000"
              @change="handelRowDel(scope.row.isDel, scope.row)"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column v-slot="scope" label="评论内容" min-width="500">
        <div
          v-if="scope.row.reply_info && scope.row.reply_info.reply_info_content"
          class="reply_info"
        >
          <span class="span-html" v-html="scope.row.reply_info.reply_info_content"></span>
        </div>
        <p :class="scope.row.reply_info ? 'replyHide' : ''">
          <span v-if="scope.row.reply_info" class="reply">回复</span>
          <span class="span-html" v-html="scope.row.content"></span>
        </p>
      </el-table-column>
      <el-table-column v-slot="scope" align="center" width="60">
        <el-popover v-if="scope.row.expression_url" trigger="hover" placement="top" width="auto">
          <div style="padding: 0 10px">
            <img
              style="height: 150px; cursor: pointer; margin: 0 auto; display: block"
              :src="scope.row.expression_url"
            />
          </div>

          <template #reference>
            <el-image
              style="width: 50px; height: 50px; cursor: pointer"
              :src="scope.row.expression_url"
              fit="cover"
            />
          </template>
        </el-popover>
        <el-popover v-if="scope.row.pic_url" trigger="hover" placement="top" width="auto">
          <div style="padding: 0 10px">
            <img
              style="height: 450px; cursor: pointer; margin: 0 auto; display: block"
              :src="scope.row.pic_url"
            />
          </div>
          <template #reference>
            <el-image
              style="width: 50px; height: 50px; cursor: pointer"
              :src="scope.row.pic_url"
              fit="cover"
            />
          </template>
        </el-popover>
      </el-table-column>
      <el-table-column v-slot="scope" label="稿件标题" :show-overflow-tooltip="true" width="500">
        <span
          v-if="scope.row.article_title"
          class="elem"
          @click="openLink(scope.row.article_url)"
          >{{ scope.row.article_title }}</span
        >
        <span v-else class="elem" @click="openLink(scope.row.article_url)">无标题</span>
      </el-table-column>
      <el-table-column v-slot="scope" width="150" label="评论人">
        <span class="comment-user" @click="showUserDetail(scope.row)">{{
          scope.row.comment_account
        }}</span>
      </el-table-column>
      <el-table-column prop="source" label="来源频道" width="120" />
      <el-table-column prop="created_at" label="评论时间" width="200" />
      <el-table-column v-slot="scope" label="操作" width="140" fixed="right">
        <el-button
          v-auth="'comment:batch_action_audit'"
          text
          style="padding-left: 0"
          @click="adoptClick(scope.row)"
        >
          <span class="button_text">通过</span>
        </el-button>
        <el-button
          v-auth="'comment:batch_action_audit'"
          text
          style="margin-left: 0"
          @click="deleteClick(scope.row.id)"
        >
          <span class="button_text">删除</span>
        </el-button>
      </el-table-column>
    </el-table>
  </div>

  <!-- 分页 -->
  <div class="page">
    <el-pagination
      background
      small
      layout="total, sizes, prev, pager, next, jumper"
      :total="data.total"
      :page-size="data.size"
      :current-page="data.current"
      :page-sizes="[10, 20, 50, 100]"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
    </el-pagination>
  </div>
  <el-dialog v-model="dialogVisible" title="确定批量处理以下评论?" width="30%">
    <div class="tip">
      通过<i>{{ data.idsPass.length }}</i
      >条，删除<b>{{ data.idsDel.length }}</b
      >条
    </div>
    <br />
    <span>通过后，可在"已通过"列表内删除，删除后，可在"已删除"页面撤销</span>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleDialogCancel">取消</el-button>
        <el-button type="primary" @click="handleDialogPass"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog v-model="dialogVisiblBatch" title="操作提示" width="30%">
    <div class="tip">
      当前批量处理评论：通过<b>{{ batchResults.passSuccessCount }}</b
      >条，删除<i>{{ batchResults.deleteTotalCount }}</i
      >条。操作失败<b>{{ batchResults.passFailCount }}</b
      >条。原因【选中的评论上级尚未获得通过状态】。
    </div>
    <br />
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="dialogVisiblBatch = false"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 评论人详情弹窗 -->
  <el-dialog v-model="userDetailVisible" title="查看详情" width="400px">
    <div class="user-detail">
      <div class="detail-row">
        <span class="label">评论人昵称：</span>
        <span class="value">{{ userDetail.nickname || '-' }}</span>
      </div>
      <div class="detail-row">
        <span class="label">评论ID：</span>
        <span class="value">{{ userDetail.commentId || '-' }}</span>
      </div>
      <div class="detail-row">
        <span class="label">手机号：</span>
        <span class="value">{{ userDetail.phone || '-' }}</span>
      </div>
      <div class="detail-row">
        <span class="label">IP地址：</span>
        <span class="value">{{ userDetail.ipAddress || '-' }}</span>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="userDetailVisible = false">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { requests } from '@/api/business/auditManagement'
import headModel from './modules/headModule.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { computed, nextTick, ref, watch, markRaw } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute(),
  router = useRouter()
const loading = ref(false)
const passBut = ref(false)
const delBut = ref(false)
const dialogVisible = ref(false)
const dialogVisiblBatch = ref(false)
const batchResults = ref({})
// 评论人详情弹窗相关
const userDetailVisible = ref(false)
const userDetail = ref({})
// 搜索栏
const searchBox = ref(null)
const id = ref(route.query.id)
const data = ref({
  height: null,
  idsPass: [], // 多选的通过数据
  idsDel: [], // 多选的删除数据
  total: 0, // 总条目数
  size: 100, // 每页显示条目
  current: 1, // 当前页码
  records: [] // 列表数据
})

// 计算属性：已选项数量
const selectedItemsCount = computed(() => {
  return data.value.idsPass.length + data.value.idsDel.length > 1
})

onMounted(() => {
  // 设置表格高度
  calculateTableHeight()
})

// 计算表格高度
function calculateTableHeight() {
  nextTick(() => {
    const tableContainer = document.getElementById('tabel-component')
    const pagination = document.getElementsByClassName('el-pagination')[0]
    if (tableContainer && pagination) {
      data.value.height = tableContainer.offsetHeight - pagination.offsetHeight + 30
    }
  })
}

// 通过全选
function handelPass() {
  if (passBut.value) {
    delBut.value = false
    data.value.records.forEach(record => {
      record.isPass = true
      record.isDel = false
    })
  } else {
    data.value.records.forEach(record => {
      record.isPass = false
    })
  }
  updateSelectedCounts()
}

// 高级标题跳转
function openLink(url) {
  window.open(url)
}

// 删除全选
function handelDel() {
  if (delBut.value) {
    passBut.value = false
    data.value.records.forEach(record => {
      record.isDel = true
      record.isPass = false
    })
  } else {
    data.value.records.forEach(record => {
      record.isDel = false
    })
  }
  updateSelectedCounts()
}

// 单项通过勾选
function handelRowPass(flag, row) {
  if (flag) {
    row.isDel = false
  }
  updateSelectedCounts()
}

// 单项删除勾选
function handelRowDel(flag, row) {
  if (flag) {
    row.isPass = false
  }
  updateSelectedCounts()
}

// 更新选中的通过和删除数量
function updateSelectedCounts() {
  const passItems = []
  const deleteItems = []

  data.value.records.forEach(record => {
    if (record.isPass) {
      passItems.push(record)
    } else if (record.isDel) {
      deleteItems.push(record)
    }
  })

  data.value.idsPass = passItems
  data.value.idsDel = deleteItems

  if (searchBox.value && searchBox.value.batchNumber) {
    searchBox.value.batchNumber.pass = passItems.length
    searchBox.value.batchNumber.del = deleteItems.length
  }
}

// 搜索接口
const search = val => {
  let searchParams = val

  // 如果是稿件标题搜索
  if (val.comment_search_type === 'ARTICLE_TITLE') {
    searchParams = { ...val }
    searchParams.comment_search_type = 'ARTICLE_ID'

    if (searchBox.value && searchBox.value.title_search_data) {
      const titleData = searchBox.value.title_search_data
      const startIdx = titleData.indexOf('【') + 1
      const endIdx = titleData.indexOf('】')

      if (startIdx > 0 && endIdx > startIdx) {
        searchParams.searchword = titleData.slice(startIdx, endIdx)
      }
    }
  }

  loading.value = true

  requests
    .list(searchParams)
    .then(res => {
      // 重置选择状态
      resetSelectionState()

      loading.value = false
      const records = res.data.pg.records || []

      // 处理返回数据
      records.forEach(record => {
        record.isPass = false
        record.isDel = false
        record.sensitives = ''
        record.sensitives2 = ''
      })

      data.value.records = records
      data.value.total = res.data.pg.total
      data.value.current = searchParams.current
    })
    .catch(() => {
      loading.value = false
    })
}

// 重置选择状态
function resetSelectionState() {
  data.value.idsPass = []
  data.value.idsDel = []
  passBut.value = false
  delBut.value = false

  if (searchBox.value && searchBox.value.batchNumber) {
    searchBox.value.batchNumber.pass = 0
    searchBox.value.batchNumber.del = 0
  }
}

// 批量审核
const auditClick = () => {
  dialogVisible.value = true
}

// 批量审核确认后的处理
const handleDialogPass = () => {
  dialogVisible.value = false

  // 准备参数
  const passIds = data.value.idsPass.map(item => item.id).join(',')
  const deleteIds = data.value.idsDel.map(item => item.id).join(',')

  loading.value = true

  requests
    .batchActionAudit({
      comment_ids_pass: passIds,
      comment_ids_Delete: deleteIds
    })
    .then(res => {
      loading.value = false
      resetSelectionState()

      if (res.code === 0 && res.data.result.passFailCount === 0) {
        ElMessage({
          type: 'success',
          message: '批量审核成功！'
        })
      } else {
        ElMessage({
          type: 'error',
          message: '批量审核失败！'
        })
        dialogVisiblBatch.value = true
        batchResults.value = res.data.result
      }

      // 刷新数据
      search(searchBox.value.searchData)
    })
    .catch(() => {
      loading.value = false
      ElMessage({
        type: 'error',
        message: '请求失败！'
      })
    })
}

// 取消处理
const handleDialogCancel = () => {
  dialogVisible.value = false
  ElMessage({
    type: 'info',
    message: '已取消！'
  })
}

// 单条通过
const adoptClick = val => {
  ElMessageBox.confirm('评论内容：' + val.content, '确定通过该评论？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'Warning',
    dangerouslyUseHTMLString: true
  })
    .then(() => {
      loading.value = true

      // 通过接口
      requests
        .batchAction({
          comment_ids: val.id,
          status: 'PASS',
          current_status: 'PENDING'
        })
        .then(res => {
          loading.value = false

          if (res.code === 0) {
            search(searchBox.value.searchData)
            ElMessage({
              type: 'success',
              message: '通过成功！'
            })
          }
        })
        .catch(() => {
          loading.value = false
        })
    })
    .catch(() => {})
}

// 单条删除
const deleteClick = val => {
  ElMessageBox.confirm('删除后，可在"已删除"页面撤销', '确定删除该评论？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'Warning',
    icon: markRaw(Delete)
  })
    .then(() => {
      loading.value = true

      // 删除接口
      requests
        .batchAction({
          comment_ids: val,
          status: 'DELETE',
          current_status: 'PENDING'
        })
        .then(res => {
          loading.value = false

          if (res.code === 0) {
            ElMessage({
              type: 'success',
              message: '删除成功！'
            })
            // 刷新数据
            search(searchBox.value.searchData)
          } else {
            ElMessage({
              type: 'error',
              message: '删除失败！'
            })
          }
        })
        .catch(() => {
          loading.value = false
          ElMessage({
            type: 'error',
            message: '删除失败！'
          })
        })
    })
    .catch(() => {})
}

// 选择每页几条
const handleSizeChange = val => {
  resetSelectionState()
  if (searchBox.value) {
    searchBox.value.searchData.size = val
    searchBox.value.searchData.current = 1
  }
  data.value.size = val
  data.value.current = 1
  search(searchBox.value.searchData)
}

// 点击分页器
const handleCurrentChange = val => {
  resetSelectionState()
  data.value.current = val
  if (searchBox.value) {
    searchBox.value.searchData.current = val
  }
  search(searchBox.value.searchData)
}

// 显示评论人详情
const showUserDetail = row => {
  // 先显示基本信息
  userDetail.value = {
    nickname: row.comment_account,
    commentId: row.id,
    phone: '-',
    ipAddress: '-'
  }
  userDetailVisible.value = true

  // 调用接口获取详细信息
  getUserDetail(row.comment_user_id)
}

// 获取评论人详情接口
const getUserDetail = async id => {
  try {
    // 调用获取评论人详情接口
    const response = await requests.getUserDetail({ comment_user_id: id })

    userDetail.value = {
      nickname: response.data.comment.comment_user_name || userDetail.value.nickname,
      commentId: response.data.comment.id || userDetail.value.commentId,
      phone: response.data.comment.phone_number || '-',
      ipAddress: response.data.comment.extra ? JSON.parse(response.data.comment.extra).location || '-' : '-'
    }
  } catch (error) {
    console.error('获取评论人详情失败:', error)
    ElMessage({
      type: 'error',
      message: '获取评论人详情失败'
    })
  }
}
</script>
<style>
.custom-tooltip {
  background-color: white !important;
  border: none !important;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.5);
}

.custom-tooltip .el-popper__arrow:before {
  display: none;
}
.replyHide {
  margin: 10px 0;
}
</style>
<style lang="scss" scoped>
a {
  color: #0b82fd;
}

.page-main {
  border-radius: 12px;
  display: flex;
  flex-direction: column;

  .reply_info {
    font-size: 14px;
    line-height: 22px;
    background: #f5f5f5;
    border-radius: 6px;
    padding: 3px 5px;
    color: #666;
  }

  .replyHide {
    padding: 0 10px;
  }

  .reply {
    background: rgba(102, 177, 253, 0.23);
    border-radius: 2px;
    border: 1px solid #0b82fd;
    margin-right: 6px;
    font-size: 12px;
    color: #0b82fd;
    padding: 2px;
    font-family: 'Arial';
  }

  .elemLink {
    color: #606266;
    text-decoration: none;
  }

  .elem:hover,
  .reviewed:hover {
    color: rgba(11, 130, 253, 0.7);
  }

  .elem {
    cursor: pointer;
  }

  :deep(.el-table) {
    height: 100%;

    .el-table__body-wrapper {
      overflow-y: auto;
    }
  }
}

.checkBlue,
.checkRed {
  width: 30px;
  display: flex;
  font-weight: 600;
  color: #333;

  .el-checkbox.el-checkbox--large {
    height: 30px;
  }
}

#tabel-component {
  height: 100%;
}

.page {
  display: flex;
  justify-content: center;
  height: 45px;
}

.tip {
  i {
    color: #409eff;
    font-weight: bold;
    font-style: normal;
    margin: 0 10px;
  }

  b {
    color: red;
    font-style: normal;
    font-weight: bold;
    margin: 0 10px;
  }
}

// 评论人点击样式
.comment-user {
  color: #0b82fd;
  cursor: pointer;

  &:hover {
    color: rgba(11, 130, 253, 0.7);
    text-decoration: underline;
  }
}

// 评论人详情弹窗样式
.user-detail {
  .detail-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .label {
      width: 100px;
      font-weight: 500;
      color: #333;
      flex-shrink: 0;
    }

    .value {
      color: #666;
      word-break: break-all;
    }
  }
}
</style>
