<template>
  <headModel
    ref="searchBox"
    :disabled="data.idsAll.length > 1"
    :data-ids-pass="data.idsAll.length"
    @search="search"
    @deleteClick="deleteClick"
    @batchDeleteClick="batchDeleteClick"
  />
  <div id="tabel-component">
    <el-table
      v-loading="loading"
      :data="data.records"
      :height="data.height"
      size="default"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" label="全选" width="40" />
      <el-table-column
        align="center"
        prop="auto_pk"
        label="ID"
        width="100"
        :show-overflow-tooltip="true"
      />
      <el-table-column v-slot="scope" label="评论内容" min-width="500">
        <div v-if="scope.row.reply_info?.reply_info_content" class="reply_info">
          <span class="span-html" v-html="scope.row.reply_info.reply_info_content"></span>
        </div>
        <p :class="{ replyHide: scope.row.reply_info }">
          <span v-if="scope.row.reply_info" class="reply">回复</span>
          <span class="span-html" v-html="scope.row.content"></span>
        </p>
      </el-table-column>
      <el-table-column v-slot="scope" align="center" width="60">
        <el-popover v-if="scope.row.expression_url" trigger="hover" placement="top" width="auto">
          <div class="popover-image-container">
            <img class="popover-image" :src="scope.row.expression_url" />
          </div>

          <template #reference>
            <el-image class="thumbnail-image" :src="scope.row.expression_url" fit="cover" />
          </template>
        </el-popover>

        <el-popover v-if="scope.row.pic_url" trigger="hover" placement="top" width="auto">
          <div class="popover-image-container">
            <img class="popover-image-large" :src="scope.row.pic_url" />
          </div>

          <template #reference>
            <el-image class="thumbnail-image" :src="scope.row.pic_url" fit="cover" />
          </template>
        </el-popover>
      </el-table-column>
      <el-table-column v-slot="scope" label="稿件标题" :show-overflow-tooltip="true" width="400">
        <span class="elem clickable" @click="openLink(scope.row.article_url)">
          {{ scope.row.article_title || '无标题' }}
        </span>
      </el-table-column>
      <el-table-column v-slot="scope" label="评论人" width="120">
        <SpammerMarker
          :disabled="true"
          v-model:spammer="scope.row.spammer"
          :userData="{
            user_name: scope.row.comment_account,
            user_id: scope.row.comment_account_id
          }"
        >
          <template #reference>
            <span class="comment-user" @click="showUserDetail(scope.row)">{{ scope.row.comment_account }}</span>
          </template>
        </SpammerMarker>
      </el-table-column>
      <el-table-column prop="source" label="来源频道" width="95" />
      <el-table-column prop="created_at" label="评论时间" width="170" />
      <el-table-column v-slot="scope" label="审核人" width="150">
        <div class="reviewed clickable" @click="handleHistory(scope.row)">
          <span class="mr-10">{{ scope.row.audit_admin_name }}</span>
        </div>
      </el-table-column>
      <el-table-column prop="audit_time" label="审核时间" width="170" />
      <el-table-column v-slot="scope" label="操作" width="200" fixed="right">
        <el-button
          v-if="scope.row.comment_level === 1 && !scope.row.top"
          v-auth="'comment_top:set_top'"
          class="action-button"
          text
          @click="setHotComment(scope.row)"
        >
          <span class="button_text">设为热评</span>
        </el-button>
        <el-button
          v-if="scope.row.comment_level === 1 && scope.row.top"
          v-auth="'comment_top:set_top'"
          class="action-button"
          text
          @click="cancelHotComment(scope.row)"
        >
          <span class="button_text">取消热评</span>
        </el-button>
        <el-button
          v-auth="'comment:batch_action_delete'"
          class="action-button"
          text
          @click="deleteClick(scope.row)"
        >
          <span class="button_text">删除</span>
        </el-button>
        <el-button
          v-auth="'comment:editor_replay'"
          class="action-button"
          text
          @click="replyClick(scope.row)"
        >
          <span class="button_text">回复</span>
        </el-button>
      </el-table-column>
    </el-table>
  </div>

  <!-- 分页 -->
  <div class="page">
    <el-pagination
      small
      background
      layout="total, sizes, prev, pager, next, jumper"
      :total="data.total"
      :page-size="data.size"
      :current-page="data.current"
      :page-sizes="[10, 20, 50, 100]"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>

  <!-- 组件引用 -->
  <OperationLog ref="operationLog" />
  <DeleteAlert
    ref="deleteAlert"
    :delete-alert-data="deleteAlertData"
    @search="search"
    @deleteClickFun="deleteClickFun"
  />
  <!-- 回复评论 -->
  <Reply ref="replyBox" :reply-data="replyData" @replySearch="replySearch" />

  <!-- 批量删除确认弹窗 -->
  <el-dialog v-model="dialogVisible" title="确定批量删除评论?" width="30%">
    <div class="tip">
      已选中评论<b>{{ data.idsAll.length }}</b
      >条
    </div>
    <br />
    <span class="info">删除后，可在"已删除"页面撤销</span>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleBatchDelete">确认</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 评论人详情弹窗 -->
  <el-dialog v-model="userDetailVisible" title="查看详情" width="400px">
    <div class="user-detail">
      <div class="detail-row">
        <span class="label">评论人昵称：</span>
        <span class="value">{{ userDetail.nickname || '-' }}</span>
      </div>
      <div class="detail-row">
        <span class="label">评论ID：</span>
        <span class="value">{{ userDetail.commentId || '-' }}</span>
      </div>
      <div class="detail-row">
        <span class="label">手机号：</span>
        <span class="value">{{ userDetail.phone || '-' }}</span>
      </div>
      <div class="detail-row">
        <span class="label">IP地址：</span>
        <span class="value">{{ userDetail.ipAddress || '-' }}</span>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="userDetailVisible = false">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import OperationLog from '@/components/business/operationLog.vue'
import DeleteAlert from '@/components/business/deleteAlert.vue'
import { requests } from '@/api/business/auditManagement'
import headModel from './modules/headModule.vue'
import Reply from './modules/reply.vue'
import SpammerMarker from '@/components/SpammerMarker/index.vue'

// ===== 组件状态 =====
const loading = ref(false)
const searchBox = ref(null)
const replyBox = ref(null)
const replyData = ref(null)
const operationLog = ref(null)
const deleteAlert = ref(null)
const dialogVisible = ref(false)

// 评论人详情弹窗相关
const userDetailVisible = ref(false)
const userDetail = ref({})

// 表格数据
const data = reactive({
  height: null,
  idsAll: [], // 多选中的所有数据
  total: 0, // 总条目数
  size: 100, // 每页显示条目
  current: 1, // 当前页码
  records: [] // 列表数据
})

// 传入删除弹窗的数据
const deleteAlertData = reactive({
  type: '已通过', // 判断哪个页面进去的
  comment_id: '', // 删除接口需要的comment_id
  comment_level: '',
  article_id: '',
  top_comment_id: '',
  second_comment_id: ''
})

// ===== 生命周期钩子 =====
onMounted(() => {
  calculateTableHeight()
})

// ===== 辅助函数 =====
// 计算表格高度
const calculateTableHeight = () => {
  const tableComponent = document.getElementById('tabel-component')
  if (!tableComponent) return

  const height = tableComponent.offsetHeight
  const paginationHeight = document.getElementsByClassName('el-pagination')[0]?.offsetHeight || 30
  data.height = height - (paginationHeight - 30)
}

// 打开链接
const openLink = url => {
  if (!url) return
  window.open(url)
}

// ===== 事件处理函数 =====
// 历史记录
const handleHistory = row => {
  if (!operationLog.value) return

  operationLog.value.message.auto_pk = row.auto_pk
  operationLog.value.message.id = row.id
  operationLog.value.message.content = row.content
  operationLog.value.getList()
  operationLog.value.drawer = true
}

// 表格选择变更
const handleSelectionChange = val => {
  data.idsAll = val
}

// 批量删除点击
const batchDeleteClick = () => {
  if (data.idsAll.length === 0) {
    ElMessage.warning('请先选择要删除的评论')
    return
  }
  dialogVisible.value = true
}

// 执行批量删除
const handleBatchDelete = async () => {
  if (data.idsAll.length === 0) return

  const batchCommentIds = data.idsAll.map(item => item.id).join(',')
  dialogVisible.value = false
  loading.value = true

  try {
    const res = await requests.batchAction({
      comment_ids: batchCommentIds,
      status: 'DELETE',
      current_status: 'PASS'
    })

    if (res.code === 0) {
      ElMessage.success('批量删除成功！')
    } else {
      ElMessage.error(`批量删除失败：${res.msg || ''}`)
    }
  } catch (error) {
    console.error('批量删除请求失败', error)
    ElMessage.error('请求失败，请稍后重试')
  } finally {
    loading.value = false
    // 重新加载列表
    search(searchBox.value.searchData)
  }
}

// 单条删除
const deleteClick = row => {
  // 设置删除参数
  deleteAlertData.comment_id = row.comment_id
  deleteAlertData.comment_level = row.comment_level
  deleteAlertData.article_id = row.article_id
  deleteAlertData.top_comment_id = row.top_comment_id
  deleteAlertData.second_comment_id = row.second_comment_id

  // 显示弹窗
  deleteAlert.value.dialogShow = true
}

// 执行删除
const deleteClickFun = async () => {
  loading.value = true

  try {
    const res = await requests.batchAction({
      comment_ids: deleteAlertData.comment_id,
      status: 'DELETE',
      current_status: 'PASS',
      article_id: deleteAlertData.article_id,
      top_comment_id: deleteAlertData.top_comment_id,
      second_comment_id: deleteAlertData.second_comment_id,
      comment_level: deleteAlertData.comment_level
    })

    if (res.code === 0) {
      deleteAlert.value.commentTableShow = false
      deleteAlert.value.dialogShow = false
      ElMessage.success('删除成功！')
    } else {
      ElMessage.error(`删除失败：${res.msg || ''}`)
    }
  } catch (error) {
    console.error('删除评论失败', error)
    ElMessage.error('删除失败！')
  } finally {
    loading.value = false
    // 重新加载列表
    search(searchBox.value.searchData)
  }
}

// 页码改变
const handleCurrentChange = val => {
  data.current = val
  searchBox.value.searchData.current = val
  search(searchBox.value.searchData)
}

// 每页条数改变
const handleSizeChange = val => {
  searchBox.value.searchData.size = val
  searchBox.value.searchData.current = 1
  data.size = val
  data.current = 1
  search(searchBox.value.searchData)
}

// 回复评论
const replyClick = row => {
  replyData.value = row
  replyBox.value.drawer = true
}

// 回复后刷新列表
const replySearch = () => {
  search(searchBox.value.searchData)
}

// 取消热评
const cancelHotComment = row => {
  ElMessageBox.confirm('确定要将该评论取消热评吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        await requests.setHot({
          action: 'OFF',
          comment_id: row.id,
          article_id: row.article_id
        })

        ElMessage.success('取消热评成功！')
        search(searchBox.value.searchData)
      } catch (error) {
        console.error('取消热评失败', error)
        ElMessage.error('取消热评失败！')
      }
    })
    .catch(() => {
      ElMessage.info('已取消操作')
    })
}
// 设为热评
const setHotComment = row => {
  ElMessageBox.confirm('确定要将该评论设为热评吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        await requests.setHot({
          action: 'ON',
          comment_id: row.id,
          article_id: row.article_id
        })

        ElMessage.success('设置热评成功！')
        search(searchBox.value.searchData)
      } catch (error) {
        console.error('设置热评失败', error)
      }
    })
    .catch(() => {
      ElMessage.info('已取消操作')
    })
}

// 搜索接口
const search = async params => {
  loading.value = true
  let searchParams = params
  // 处理稿件标题搜索的特殊情况
  if (params.comment_search_type === 'ARTICLE_TITLE') {
    searchParams = { ...params }
    searchParams.comment_search_type = 'ARTICLE_ID'

    if (searchBox.value.titleSearchData) {
      const title = searchBox.value.titleSearchData
      const startIndex = title.indexOf('【') + 1
      const endIndex = title.indexOf('】')

      if (startIndex > 0 && endIndex > startIndex) {
        searchParams.searchword = title.slice(startIndex, endIndex)
      }
    }
  }

  try {
    const res = await requests.list(searchParams)
    // 更新数据
    data.records = res.data.pg.records
    data.total = res.data.pg.total
    data.current = searchParams.current
  } catch (error) {
    console.error('加载评论列表失败', error)
  } finally {
    loading.value = false
  }
}

// 显示评论人详情
const showUserDetail = row => {
  // 先显示基本信息
  userDetail.value = {
    nickname: row.comment_account,
    commentId: row.id,
    phone: '-',
    ipAddress: '-'
  }
  userDetailVisible.value = true

  // 调用接口获取详细信息
  getUserDetail(row.comment_user_id)
}

// 获取评论人详情接口
const getUserDetail = async id => {
  try {
    // 调用获取评论人详情接口
    const response = await requests.getUserDetail({ comment_user_id: id })

    userDetail.value = {
      nickname: response.data.comment.comment_user_name || userDetail.value.nickname,
      commentId: response.data.comment.id || userDetail.value.commentId,
      phone: response.data.comment.phone_number || '-',
      ipAddress: response.data.comment.extra ? JSON.parse(response.data.comment.extra).location || '-' : '-'
    }
  } catch (error) {
    console.error('获取评论人详情失败:', error)
    ElMessage({
      type: 'error',
      message: '获取评论人详情失败'
    })
  }
}
</script>

<style lang="scss" scoped>
/* 通用样式 */
.clickable {
  cursor: pointer;
  &:hover {
    color: rgba(11, 130, 253, 0.7);
  }
}

.mr-10 {
  margin-right: 10px;
}

.action-button {
  padding-left: 0;
  margin-left: 0;
}

/* 表格图片样式 */
.thumbnail-image {
  width: 50px;
  height: 50px;
  cursor: pointer;
}

.popover-image-container {
  padding: 0 10px;
}

.popover-image {
  height: 150px;
  cursor: pointer;
  margin: 0 auto;
  display: block;
}

.popover-image-large {
  height: 450px;
  cursor: pointer;
  margin: 0 auto;
  display: block;
}

/* 表格内容样式 */
.reply_info {
  font-size: 14px;
  line-height: 22px;
  background: #f5f5f5;
  border-radius: 6px;
  padding: 3px 5px;
  color: #666;
}

.replyHide {
  padding: 0 10px;
}

.reply {
  background: rgba(102, 177, 253, 0.23);
  border-radius: 2px;
  border: 1px solid #0b82fd;
  margin-right: 6px;
  font-size: 12px;
  color: #0b82fd;
  padding: 2px;
  font-family: 'Arial';
}

.reviewed {
  display: flex;
  align-items: center;
  width: 100%;
}

/* 情感倾向指示器 */
.emotion {
  font-size: 14px;
  font-weight: 400;
  color: #000000;
  line-height: 22px;
  position: relative;
  display: flex;
  align-items: center;

  &::before {
    content: ' ';
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 6px;
  }

  &.unknow::before {
    background: #8a8a8a;
  }

  &.centre::before {
    background: #177fef;
  }

  &.straight::before {
    background: #27d6c5;
  }

  &.burden::before {
    background: #fc4f0d;
  }
}

/* 弹窗样式 */
::v-deep .el-dialog__body {
  padding-top: 10px;
  padding-bottom: 10px;
}

.deleteAlert {
  .dialogTableVisibleTitle1,
  .dialogTableVisibleTitle2,
  .dialogTableVisibleTitle3 {
    span {
      display: inline-block;
      margin-left: 15px;
      color: #409eff;
      cursor: pointer;
    }
  }

  .dialogTableVisibleTitle2 {
    margin: 20px 0;
  }

  .dialogTableVisibleTitle3 {
    margin-bottom: 0;
    color: #409eff;
  }

  .dialogPage {
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }
}

/* 布局样式 */
#tabel-component {
  height: 100%;
}

.page {
  display: flex;
  justify-content: center;
  height: 45px;
}

/* 删除提示样式 */
.tip {
  i {
    color: #409eff;
    font-weight: bold;
    font-style: normal;
    margin: 0 10px;
  }

  b {
    color: red;
    font-style: normal;
    font-weight: bold;
    margin: 0 10px;
  }
}

.info {
  color: #f59a23;
}

/* 评论人详情弹窗样式 */
.user-detail {
  .detail-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .label {
      width: 100px;
      font-weight: 500;
      color: #333;
      flex-shrink: 0;
    }

    .value {
      color: #666;
      word-break: break-all;
    }
  }
}

// 评论人点击样式
.comment-user {
  color: #0b82fd;
  cursor: pointer;

  &:hover {
    color: rgba(11, 130, 253, 0.7);
    text-decoration: underline;
  }
}
</style>
