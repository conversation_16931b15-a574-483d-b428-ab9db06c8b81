<template>
  <el-drawer
    id="drawer"
    v-model="drawer"
    title="用户回复"
    :with-header="true"
    show-close="true"
    size="700px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formState"
      :rules="formRules"
      label-width="120px"
      class="reply-form"
    >
      <!-- 稿件信息区域 -->
      <el-form-item label="稿件标题：">
        <p class="text" v-html="props.replyData?.article_title || '无标题'"></p>
      </el-form-item>

      <!-- 回复评论区域 -->
      <el-form-item label="回复评论：">
        <div class="comment-info">
          <p class="text bold" v-html="props.replyData?.comment_account || ''"></p>
          <p class="text" v-html="props.replyData?.content || ''"></p>
        </div>
      </el-form-item>

      <!-- 回复内容编辑区 -->
      <el-form-item prop="comment_content">
        <el-input
          v-model="formState.comment_content"
          type="textarea"
          maxlength="250"
          show-word-limit
          :rows="10"
          resize="none"
          placeholder="请输入回复内容"
          @input="resetSensitiveWords"
        />
        <div v-if="sensitiveWords" class="sensitive-warning">{{ sensitiveWords }}</div>
      </el-form-item>

      <!-- 评论人选择区 -->
      <el-form-item label="评论人" prop="selectedVest">
        <!-- 搜索选择框 -->
        <el-select
          v-model="formState.selectedVest"
          filterable
          remote
          clearable
          reserve-keyword
          placeholder="请输入评论人"
          :remote-method="fetchVestList"
          :loading="loading"
          class="vest-select"
          @change="handleVestChange"
        >
          <el-option
            v-for="item in vestOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>

        <!-- 设为常用按钮 -->
        <el-button
          :disabled="isSetToCommonDisabled"
          text
          class="no-hover-effect"
          @click="addToFavorites"
        >
          <span class="button-text">+ 设为常用</span>
        </el-button>

        <!-- 常用评论人列表 -->
        <div class="user-list">
          <el-tooltip
            v-for="item in favoriteVests"
            :key="item.id"
            :content="item.nick_name"
            placement="top"
            effect="dark"
          >
            <div
              :class="['user-item', { active: item.id === activeVestId }]"
              @click="selectFavoriteVest(item)"
            >
              <span class="user-name">{{ item.nick_name }}</span>
              <el-icon class="delete-icon" @click.stop="confirmDeleteVest(item.auto_pk)">
                <Close />
              </el-icon>
            </div>
          </el-tooltip>
        </div>
      </el-form-item>
    </el-form>

    <!-- 底部按钮区 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="drawer = false">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="submitReply">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import 'emoj-vue-chao/css/emoji-mart.css'
import { requests } from '@/api/business/reply'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { Close } from '@element-plus/icons-vue'

// 定义组件属性
const props = defineProps({
  replyData: {
    type: Object,
    default: () => ({})
  }
})

// 定义组件事件
const emit = defineEmits(['replySearch'])

// 表单引用
const formRef = ref(null)

// 状态管理
const drawer = ref(false)
const loading = ref(false)
const submitting = ref(false)
const sensitiveWords = ref('')
const vestList = ref([])
const favoriteVests = ref([])
const vestOptions = ref([])
const activeVestId = ref(null)

// 表单状态和规则
const formState = reactive({
  comment_content: '',
  selectedVest: null
})

const formRules = {
  comment_content: [
    { required: true, message: '请输入回复内容', trigger: 'blur' },
    { max: 250, message: '回复内容不能超过250个字符', trigger: 'blur' }
  ],
  selectedVest: [
    { required: true, message: '请选择评论人', trigger: 'change' }
  ]
}

// 计算属性
const selectedVest = computed(() => {
  if (!formState.selectedVest) return null
  return vestList.value.find(item => item.id === formState.selectedVest) || null
})

const isSetToCommonDisabled = computed(() => {
  return favoriteVests.value.length >= 10 || !selectedVest.value
})

// 监听器
watch(() => props.replyData, () => {
  if (drawer.value) {
    resetForm()
  }
}, { deep: true })

// 生命周期钩子
onMounted(() => {
  loadFavoriteVests()
})

// 方法
// 加载常用评论人列表
const loadFavoriteVests = async () => {
  try {
    const res = await requests.vest_collect_list({ size: 10 })
    favoriteVests.value = res.data.vest_list_collect || []
  } catch (error) {
    console.error('获取常用评论人列表失败:', error)
    ElMessage.error('获取常用评论人列表失败')
  }
}

// 搜索评论人
const fetchVestList = async (query) => {
  if (!query) {
    vestOptions.value = []
    return
  }

  loading.value = true
  try {
    const res = await requests.vest_list({
      nick_name: query,
      size: 10
    })

    vestList.value = res.data.vest_list || []
    vestOptions.value = vestList.value.map(item => ({
      value: item.id,
      label: `${item.phone_number} ${item.nick_name}`
    }))
  } catch (error) {
    console.error('搜索评论人失败:', error)
    ElMessage.error('搜索评论人失败')
  } finally {
    loading.value = false
  }
}

// 评论人变更处理
const handleVestChange = () => {
  activeVestId.value = formState.selectedVest
}

// 添加到常用
const addToFavorites = async () => {
  if (!selectedVest.value || favoriteVests.value.length >= 10) return

  if (favoriteVests.value.some(item => item.id === selectedVest.value.id)) {
    ElMessage.info('该评论人已在常用列表中')
    return
  }

  try {
    await requests.addVestCollect({ id: selectedVest.value.id })
    ElMessage.success('添加成功！')
    await loadFavoriteVests()
  } catch (error) {
    console.error('添加常用评论人失败:', error)
    ElMessage.error('添加失败！')
  }
}

// 选择常用评论人
const selectFavoriteVest = async (item) => {
  await fetchVestList(item.nick_name)
  activeVestId.value = item.id
  formState.selectedVest = item.id
}

// 确认删除常用评论人
const confirmDeleteVest = (autoPk) => {
  ElMessageBox.confirm('是否删除常用评论人？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => deleteVest(autoPk))
    .catch(() => {})
}

// 删除常用评论人
const deleteVest = async (autoPk) => {
  try {
    await requests.deleteVestCollect({ auto_pk: autoPk })
    ElMessage.success('删除成功！')
    await loadFavoriteVests()

    // 如果删除的是当前选中的评论人，则清空选择
    if (activeVestId.value && !favoriteVests.value.some(v => v.id === activeVestId.value)) {
      activeVestId.value = null
    }
  } catch (error) {
    console.error('删除常用评论人失败:', error)
    ElMessage.error('删除失败！')
  }
}

// 关闭事件处理
const handleClose = () => {
  resetForm()
}

// 重置表单
const resetForm = () => {
  formState.comment_content = ''
  formState.selectedVest = null
  activeVestId.value = null
  sensitiveWords.value = ''

  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 重置敏感词提示
const resetSensitiveWords = () => {
  sensitiveWords.value = ''
}

// 检查敏感词
const checkSensitiveWords = async () => {
  if (!formState.comment_content) return false

  try {
    const res = await requests.checkSensitiveWord({
      comment_content: formState.comment_content
    })

    if (res.code !== 0) return false

    if (res.data.result?.checked_word_list.length) {
      const sensitiveList = res.data.result.checked_word_list.map(item => item)
      sensitiveWords.value = `发现敏感词：${sensitiveList.join(', ')}`

      try {
        await ElMessageBox.confirm(
          `经校验，发现<b style="color:red;">${sensitiveList.join(', ')}</b>为敏感词，是否确认继续提交当前回复？`,
          '提示',
          {
            cancelButtonText: '取消',
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true,
            center: true
          }
        )
        return true
      } catch {
        return false
      }
    }

    return true
  } catch (error) {
    console.error('敏感词检查失败:', error)
    ElMessage.error('敏感词检查失败')
    return false
  }
}

// 提交回复
const submitReply = async () => {
  // 表单验证
  try {
    await formRef.value.validate()
  } catch {
    return
  }

  // 先在vestList中查找
  let selectedUser = vestList.value.find(item => item.id === formState.selectedVest)

  // 如果vestList中没找到，则在favoriteVests中查找
  if (!selectedUser && formState.selectedVest) {
    selectedUser = favoriteVests.value.find(item => item.id === formState.selectedVest)
  }

  if (!formState.selectedVest || !selectedUser) {
    ElMessage.warning('请选择评论人')
    return
  }

  // 敏感词检查
  const canContinue = await checkSensitiveWords()
  if (!canContinue) return

  // 执行提交
  submitting.value = true
  try {
    const res = await requests.vest_comment_reply({
      parent_comment_id: props.replyData.id,
      top_comment_id: props.replyData.comment_level === 1 ? props.replyData.id :  props.replyData.top_comment_id,
      second_comment_id: props.replyData.comment_level === 1 ? '' :  props.replyData.id,
      comment_content: formState.comment_content,
      channel_id: props.replyData.channel_id,
      article_id: props.replyData.article_id,
      article_title: props.replyData.article_title,
      article_url: props.replyData.article_url,
      comment_user_id: selectedUser.id,
      comment_user_name: selectedUser.nick_name,
      comment_user_portrait: selectedUser.image_url,
      comment_user_location: selectedUser.location,
      comment_level: props.replyData.comment_level < 3 ? props.replyData.comment_level + 1 : 3
    })

    if (res.code === 0) {
      ElMessage.success('回复成功！')
      drawer.value = false

      // 延迟刷新列表，确保数据更新
      setTimeout(() => {
        emit('replySearch')
      }, 500)
    } else {
      ElMessage.error(res.msg || '回复失败！')
    }
  } catch (error) {
    console.error('提交回复失败:', error)
    ElMessage.error('回复失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}

// 暴露给父组件的属性和方法
defineExpose({ drawer })
</script>

<style lang="scss" scoped>
.reply-form {
  padding-right: 12px;
}

// 文本样式
.text {
  margin: 0;
  word-break: break-all;

  &.bold {
    font-weight: bold;
  }
}

// 评论信息样式
.comment-info {
  max-height: 200px;
  overflow-y: auto;
}

// 敏感词警告样式
.sensitive-warning {
  width: 100%;
  color: #f56c6c;
  font-size: 14px;
  margin-top: 5px;
}

// 评论人选择样式
.vest-select {
  width: 240px;
}

.button-text {
  color: #409eff;
}

// 用户列表样式
.user-list {
  margin-top: 10px;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;

  .user-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 120px;
    height: 32px;
    border: 1px solid #e5e5e5;
    padding: 5px 8px;
    cursor: pointer;
    border-radius: 4px;
    transition: border-color 0.2s;

    &:hover {
      border-color: #c6e2ff;
    }

    &.active {
      border-color: #409eff;
      background-color: #f5f7fa;
    }

    .user-name {
      width: 80px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .delete-icon {
      cursor: pointer;
      color: #909399;

      &:hover {
        color: #f56c6c;
      }
    }
  }
}

// 页脚样式
.drawer-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 20px;
}

// 按钮样式
.no-hover-effect {
  &:hover {
    background-color: transparent !important;
    color: inherit !important;
  }
}
</style>
