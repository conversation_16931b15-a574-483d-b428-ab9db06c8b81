<template>
  <div class="head-box">
    <div class="left">
      <div>
        <el-button type="danger" :disabled="dataIdsPass < 1" color="#FF0000" @click="emit('batchDeleteClick')">
          批量删除
        </el-button>
      </div>
      <span class="text-delete">
        删除<span class="text-danger">{{ dataIdsPass }}</span>条
      </span>
    </div>
    <div class="right">
      <!-- 时间类型选择器 -->
      <el-select v-model="searchData.time_type" style="width: 110px; margin-right: 10px">
        <el-option v-for="item in timeType" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>

      <!-- 日期时间范围选择器 -->
      <el-date-picker
        v-model="time"
        style="width: 220px; margin-right: 10px"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        :editable="false"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="defaultTime"
        :disabled-date="disabledDate"
        @change="handleTimeChange"
      />

      <!-- 圈子选择器 -->
      <el-cascader
        v-model="circleValue"
        :options="circleList"
        :show-all-levels="false"
        clearable
        collapse-tags
        :props="circleProps"
        placeholder="请选择圈子"
        style="width: 130px; margin-right: 10px"
        @change="changeCircle"
      />

      <!-- 频道选择器 -->
      <el-cascader
        v-model="cascadeValue"
        :options="options"
        :show-all-levels="false"
        clearable
        collapse-tags
        :props="channelProps"
        placeholder="全部频道"
        style="width: 130px; margin-right: 10px"
        @change="changeChannel"
        :disabled="channelDisabled"
      />

      <!-- 评论类型选择器 -->
      <el-select v-model="searchData.comment_type" style="width: 110px; margin-right: 10px">
        <el-option
          v-for="item in commentType"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        />
      </el-select>

      <el-divider direction="vertical" />

      <!-- 搜索条件选择器 -->
      <el-select
        v-model="searchData.comment_search_type"
        placeholder="请选择"
        style="width: 110px; margin-right: 5px"
        @change="handleSearchTypeChange"
      >
        <el-option
          v-for="item in commentSearchType"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        />
      </el-select>

      <!-- 普通搜索输入框 -->
      <el-input
        v-if="searchData.comment_search_type !== 'ARTICLE_TITLE'"
        v-model="searchData.searchword"
        placeholder="请输入关键词"
        style="width: 200px"
        class="input-with-select"
        clearable
        @keyup.enter="submitSearch"
      />

      <!-- 稿件标题搜索下拉框 -->
      <el-select
        v-else
        v-model="titleSearchData"
        style="width: 200px; margin-right: 0"
        filterable
        remote
        :remote-method="fetchTitleOptions"
        :loading="loading"
        clearable
        popper-class="selectElem"
        :teleported="false"
        placeholder="请输入稿件关键字"
        @change="handleTitleChange"
      >
        <el-option
          v-for="item in titleOptions"
          :key="item.key"
          :label="item.label"
          :value="item.label"
        />
      </el-select>

      <!-- 搜索按钮 -->
      <el-button
        type="primary"
        color="#0B82FD"
        :icon="Search"
        style="margin-left: 5px"
        @click="submitSearch"
      >
        搜索
      </el-button>
      <Output :type="2" :searchData="searchData" />
    </div>
  </div>
</template>

<script setup>
import { requests } from '@/api/business/auditManagement'
import { Search } from '@element-plus/icons'
import { ref, onMounted, defineEmits, defineExpose, computed, reactive } from 'vue'
import Output from '@/views/auditManagement/component/output.vue'
import { ElMessage } from 'element-plus'

// 组件事件
const emit = defineEmits(['search', 'deleteClick', 'batchDeleteClick'])

// 组件属性
const props = defineProps({
  dataIdsPass: { type: Number, default: 0 },
  disabled: { type: Number, default: 0 }
})

// ===== 状态管理 =====
// 时间选择器
const time = ref(null)
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]

// 时间选择器限制函数
const disabledDate = (date) => {
  if (!time.value || time.value.length === 0) {
    return false;
  }

  const [startTime, endTime] = time.value;
  const thirtyDaysLater = new Date(new Date(startTime).getTime() + 30 * 24 * 60 * 60 * 1000);
  const thirtyDaysBefore = new Date(new Date(endTime).getTime() - 30 * 24 * 60 * 60 * 1000);

  if (startTime && !endTime) {
    return date.getTime() > thirtyDaysLater.getTime() || date.getTime() < startTime.getTime();
  }

  if (!startTime && endTime) {
    return date.getTime() < thirtyDaysBefore.getTime() || date.getTime() > endTime.getTime();
  }

  return false;
}

// 时间变更处理函数
const handleTimeChange = (val) => {
  if (val && val.length === 2) {
    const [startTime, endTime] = val;
    const startDate = new Date(startTime);
    const endDate = new Date(endTime);

    // 确保结束日期不早于开始日期
    if (endDate < startDate) {
      time.value = [startTime, startTime];
      return;
    }

    // 检查是否超过30天
    const diffTime = Math.abs(endDate - startDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 30) {
      // 如果超过30天，将结束日期设置为开始日期后的第30天，且设置为当天的23:59:59
      const maxEndDate = new Date(startDate);
      maxEndDate.setDate(startDate.getDate() + 30);
      maxEndDate.setHours(23, 59, 59, 0);
      const formattedEndDate = maxEndDate.toISOString().slice(0, 19).replace('T', ' ');
      time.value = [startTime, formattedEndDate];

      // 使用 ElMessage 提示用户
      ElMessage({
        message: '时间范围最多为30天',
        type: 'warning'
      });
    }
  }
}

// 分类相关状态
const cascadeValue = ref(null) // 频道选值
const options = ref([]) // 频道选项
const channelProps = ref({
  value: 'category_id',
  label: 'name',
  multiple: true
})

// 圈子相关
const circleValue = ref(null) // 圈子选值
const circleList = ref([]) // 圈子选项
const circleProps = ref({
  value: 'id',
  label: 'name',
  multiple: true
})

// 标题搜索相关
const titleOptions = ref([]) // 稿件标题选项
const titleSearchData = ref('') // 稿件标题搜索值

const loading = ref(false) // 加载状态

// 计算属性：频道选择器禁用状态
const channelDisabled = computed(() => false)

// ===== 固定选项数据 =====
// 日期类型选项
const timeType = reactive([
  { key: 'COMMENT_DATE', label: '评论日期' },
  { key: 'AUDIT_DATE', label: '审核日期' }
])

// 评论类型选项
const commentType = reactive([
  { key: 'ALL', label: '全部评论' },
  { key: 'ARTICLE_TYPE', label: '稿件评论' },
  { key: 'REPLY_TYPE', label: '回复评论' }
])

// 搜索类型选项
const commentSearchType = reactive([
  { key: 'CONTENT', label: '评论内容' },
  { key: 'ARTICLE_TITLE', label: '稿件标题' },
  { key: 'COMMENT_PERSON', label: '评论人' },
  { key: 'ARTICLE_ID', label: '稿件id' }
])

// 搜索参数
const searchData = ref({
  state: 'PASS', // 状态(PENDING-待审核，PASS-审核通过，UNPASS-已删除)
  time_type: 'COMMENT_DATE', // 评论日期类型
  start_time: null, // 起始时间
  end_time: null, // 结束时间
  category_id: '',
  circle_id: '',
  comment_source: '', // 频道名称
  comment_type: 'ALL', // 筛选评论类型
  comment_search_type: 'CONTENT', // 搜索类型
  searchword: null, // 关键词
  size: 100, // 每页条数
  current: 1 // 分页页码 默认1
})

// ===== 生命周期钩子 =====
onMounted(async () => {
  try {
    // 初始搜索
    emit('search', searchData.value)
    // 并行加载所有初始数据
    await Promise.all([
      loadCategoryTree(),
      loadCircleList()
    ])

  } catch (error) {
    console.error('初始化数据加载失败:', error)
  }
})

// ===== 数据加载方法 =====
// 加载分类树
const loadCategoryTree = async () => {
  try {
    const res = await requests.listCategoryTree()
    // const categoryList = filterPermissionedCategories(res.data.category_list)
    options.value = res.data.category_list
  } catch (error) {
    console.error('加载分类树失败:', error)
  }
}

// 过滤无权限的分类
const filterPermissionedCategories = (categories) => {
  const filteredList = [...categories]

  for (let i = 0; i < filteredList.length; i++) {
    if (parseInt(filteredList[i].permission) === 0) {
      filteredList.splice(i--, 1)
      continue
    }

    if (filteredList[i].children?.length) {
      const children = filteredList[i].children
      for (let j = 0; j < children.length; j++) {
        if (children[j].permission === 0) {
          children.splice(j--, 1)
          continue
        }

        if (children[j].children?.length) {
          const grandchildren = children[j].children
          for (let k = 0; k < grandchildren.length; k++) {
            if (grandchildren[k].permission === 0) {
              grandchildren.splice(k--, 1)
            }
          }
        }
      }
    }
  }

  return filteredList
}

// 加载圈子列表
const loadCircleList = async () => {
  try {
    const res = await requests.getcircleList({})
    circleList.value = res.data.circle_list
  } catch (error) {
    console.error('加载圈子列表失败:', error)
  }
}

// ===== 事件处理方法 =====
// 搜索类型变更
const handleSearchTypeChange = () => {
  titleSearchData.value = ''
}

// 远程搜索稿件标题
const fetchTitleOptions = async (query) => {
  if (!query) {
    titleOptions.value = []
    return
  }

  loading.value = true
  try {
    const searchParams = {
      ...searchData.value,
      size: 10,
      current: 1,
      searchword: query
    }

    const res = await requests.searchList(searchParams)
    titleOptions.value = res.data.list.map(item => ({
      key: item.article_id,
      label: `【${item.article_id}】${item.article_title}`
    }))
  } catch (error) {
    console.error('搜索稿件标题失败:', error)
  } finally {
    loading.value = false
  }
}

// 频道变更
const changeChannel = () => {
  if (cascadeValue.value) {
    // 提取并去重频道ID
    const channelIds = Array.from(new Set(cascadeValue.value.join(',').split(',')))
    searchData.value.category_id = channelIds.join(',')
  } else {
    searchData.value.category_id = ''
  }

  submitSearch()
}

// 圈子变更
const changeCircle = () => {
  searchData.value.circle_id = circleValue.value ? circleValue.value.join(',') : ''
  submitSearch()
}

// 稿件标题选择
const handleTitleChange = (val) => {
  if (!val) return

  const newSearchData = { ...searchData.value }
  newSearchData.comment_search_type = 'ARTICLE_ID'

  // 提取标题中的文章ID
  const startIndex = val.indexOf('【') + 1
  const endIndex = val.indexOf('】')
  if (startIndex > 0 && endIndex > startIndex) {
    newSearchData.searchword = val.slice(startIndex, endIndex)
    emit('search', newSearchData)
  }
}

// 提交搜索
const submitSearch = () => {
  // 处理时间范围
  if (time.value) {
    searchData.value.start_time = time.value[0]
    searchData.value.end_time = time.value[1]
  } else {
    searchData.value.start_time = ''
    searchData.value.end_time = ''
  }

  // 重置为第一页
  searchData.value.current = 1

  // 处理稿件标题搜索特殊情况
  if (searchData.value.comment_search_type === 'ARTICLE_TITLE') {
    const newSearchData = { ...searchData.value }
    newSearchData.comment_search_type = 'ARTICLE_ID'

    if (titleSearchData.value) {
      const startIndex = titleSearchData.value.indexOf('【') + 1
      const endIndex = titleSearchData.value.indexOf('】')

      if (startIndex > 0 && endIndex > startIndex) {
        newSearchData.searchword = titleSearchData.value.slice(startIndex, endIndex)
        emit('search', newSearchData)
        return
      }
    }

    emit('search', newSearchData)
  } else {
    emit('search', searchData.value)
  }
}

// 暴露给父组件的属性
defineExpose({
  searchData,
  titleSearchData: titleSearchData
})
</script>

<style lang="scss" scoped>
.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;

  .left {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    white-space: nowrap;
    margin-right: 10px;

    .text-delete {
      font-size: 14px;
      margin-left: 10px;
      color: #000;
    }

    .text-danger {
      color: #ff0000;
    }
  }

  .right {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    row-gap: 6px;
  }
}
</style>

<style>
.input-with-select .el-input-group__append {
  background-color: #1890ff;
  color: #fff;
}

:deep(.selectElem) {
  width: 300px;

  .el-select-dropdown__item {
    white-space: pre-wrap;
    height: auto;
    line-height: 24px;
    padding: 5px 16px;

    &.hover,
    &:hover {
      background-color: #ebebeb;
    }
  }
}
</style>
