<template>
  <!-- 已删除 点击 回复评论弹窗 -->
  <el-dialog
    v-model="disabledShow"
    class="disabledAlert"
    size="default"
    border
    stripe
    :show-close="false"
    highlight-current-row
    :align-center="true"
    title="存在未撤销的上级评论，当前操作不被允许!"
  >
    <el-table :data="disableData" border stripe highlight-current-row>
      <el-table-column align="center" property="auto_pk_str" label="序号" width="150" />
      <el-table-column v-slot="scope" align="center" label="评论内容">
        <p v-html="scope.row.content"></p>
        <el-button style="float: right" type="primary" @click="copyAddress(scope.row.content)">
          复制
        </el-button>
      </el-table-column>
      <el-table-column v-slot="scope" align="center" label="评论内容">
        <p v-html="scope.row.content"></p>
      </el-table-column>
      <el-table-column align="center" property="comment_user_name" width="150" label="评论人" />

      <el-table-column v-slot="scope" align="center" width="150" label="操作">
        <el-button
          v-if="scope.$index === 0"
          style="padding-left: 0"
          text
          @click="deleteToPass ? deleteClick(scope.row) : deleteClick2(scope.row)"
        >
          <span class="button_text">{{ deleteToPass ? '撤销并通过' : '撤销至待审' }}</span>
        </el-button>
      </el-table-column>
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
  <headModel
    ref="searchBox"
    :disabled="data.idsAll.length > 1"
    @search="search"
    @deleteClick="deleteClick"
  />
  <div id="tabel-component">
    <el-table v-loading="loading" :data="data.records" :height="data.height">
      <el-table-column
        align="center"
        prop="auto_pk"
        label="ID"
        width="80"
        :show-overflow-tooltip="true"
      />

      <el-table-column
        v-slot="scope"
        label="评论内容"
        min-width="500"
      >
        <div
          v-if="scope.row.reply_info && scope.row.reply_info.reply_info_content"
          class="reply_info"
        >
          <span class="span-html" v-html="scope.row.reply_info.reply_info_content"></span>

        </div>
        <p :class="scope.row.reply_info ? 'replyHide' : ''">
          <span v-if="scope.row.reply_info" class="reply">回复</span>
          <span class="span-html" v-html="scope.row.content"></span>

        </p>
      </el-table-column>
      <el-table-column v-slot="scope" align="center" width="60">
        <el-popover v-if="scope.row.expression_url" trigger="hover" placement="top" width="auto">
          <div style="padding: 0 10px">
            <img
              style="height: 150px; cursor: pointer; margin: 0 auto; display: block"
              :src="scope.row.expression_url"
            />
          </div>

          <template #reference>
            <el-image
              style="width: 50px; height: 50px; cursor: pointer"
              :src="scope.row.expression_url"
              fit="cover"
            />
          </template>
        </el-popover>
        <el-popover v-if="scope.row.pic_url" trigger="hover" placement="top" width="auto">
          <div style="padding: 0 10px">
            <img
              style="height: 450px; cursor: pointer; margin: 0 auto; display: block"
              :src="scope.row.pic_url"
            />
          </div>

          <template #reference>
            <el-image
              style="width: 50px; height: 50px; cursor: pointer"
              :src="scope.row.pic_url"
              fit="cover"
            />
          </template>
        </el-popover>
      </el-table-column>
      <el-table-column
        v-slot="scope"
        label="稿件标题"
        :show-overflow-tooltip="true"
        width="400"
      >
        <span
          v-if="scope.row.article_title"
          class="elem"
          @click="openLink(scope.row.article_url)"
          >{{ scope.row.article_title }}</span
        >
        <span v-else class="elem" @click="openLink(scope.row.article_url)">无标题</span>
      </el-table-column>
      <el-table-column
        v-slot="scope"
        width="120"
        label="评论人"
      >
        <SpammerMarker
          :disabled="true"
          v-model:spammer="scope.row.spammer"
          :userData="{
            user_name: scope.row.comment_account,
            user_id: scope.row.comment_account_id
          }"
        >
          <template #reference>
            <span class="comment-user" @click="showUserDetail(scope.row)">{{ scope.row.comment_account }}</span>
          </template>
        </SpammerMarker>
      </el-table-column>
      <el-table-column prop="source" label="来源频道" width="120" />

      <el-table-column
        prop="created_at"
        label="评论时间"
        width="180"
      />
      <el-table-column
        v-slot="scope"
        label="审核人"
        width="150"
      >
        <div class="reviewed" style="" @click="handleHistory(scope.row)">
          <span style="margin-right: 10px">
            {{ scope.row.audit_admin_name }}
          </span>
        </div>
      </el-table-column>
      <el-table-column
        prop="audit_time"
        label="审核时间"
        width="180"
      />
      <el-table-column
        v-slot="scope"
        label="操作"
        width="210"
        fixed="right"
      >
        <el-button
          v-auth="'comment:batch_action_cancel_to_pass'"
          style="padding-left: 0"
          :disabled="scope.row.is_user_delete"
          text
          @click="deleteClick2(scope.row)"
        >
          <span class="button_text">撤销至待审</span>
        </el-button>
        <el-button
          v-auth="'comment:batch_action_cancel_to_pass'"
          style="padding-left: 0"
          :disabled="scope.row.is_user_delete"
          text
          @click="deleteClick(scope.row)"
        >
          <span class="button_text">撤销并通过</span>
        </el-button>
      </el-table-column>
    </el-table>
  </div>

  <!-- 分页 -->
  <div class="page">
    <el-pagination
      small
      background
      layout="total, sizes, prev, pager, next, jumper"
      :total="data.total"
      :page-size="data.size"
      :current-page="data.current"
      :page-sizes="[10, 20, 50, 100]"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
    </el-pagination>
  </div>
  <OperationLog ref="operationLog" />
  <el-dialog
    v-model="dialogShow"
    class="deleteAlert"
    :align-center="true"
    :title="deleteToPass ? '是否确认撤销并通过？' : '是否确认撤销至待审？'"
    :show-close="false"
    width="30%"
  >
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onClose"> 取消 </el-button>
        <el-button type="primary" @click="onOk"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 评论人详情弹窗 -->
  <el-dialog v-model="userDetailVisible" title="查看详情" width="400px">
    <div class="user-detail">
      <div class="detail-row">
        <span class="label">评论人昵称：</span>
        <span class="value">{{ userDetail.nickname || '-' }}</span>
      </div>
      <div class="detail-row">
        <span class="label">评论ID：</span>
        <span class="value">{{ userDetail.commentId || '-' }}</span>
      </div>
      <div class="detail-row">
        <span class="label">手机号：</span>
        <span class="value">{{ userDetail.phone || '-' }}</span>
      </div>
      <div class="detail-row">
        <span class="label">IP地址：</span>
        <span class="value">{{ userDetail.ipAddress || '-' }}</span>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="userDetailVisible = false">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import useClipboard from 'vue-clipboard3'
import OperationLog from '@/components/business/operationLog.vue'
import { requests } from '@/api/business/auditManagement'
import headModel from './modules/headModule.vue'
import { ElMessage } from 'element-plus'
import { ref, onMounted, nextTick } from 'vue'
import SpammerMarker from '@/components/SpammerMarker/index.vue'

// 复制
const { toClipboard } = useClipboard()
const copyAddress = async row => {
  try {
    toClipboard(row)
    ElMessage({
      type: 'success',
      message: '复制成功！'
    })
  } catch (e) {
    console.error(e)
  }
}

const loading = ref(false)
const searchBox = ref(null)
// 表格数据
const data = ref({
  height: null,
  idsAll: [],
  total: 0,
  size: 100,
  current: 1,
  records: []
})
const deleteToPass = ref(false)
const dialogShow = ref(false)
const operationLog = ref(null)
const disabledShow = ref(false)
const disableData = ref()
// 评论人详情弹窗相关
const userDetailVisible = ref(false)
const userDetail = ref({})
const deleteAlertData = ref({
  type: '已删除',
  comment_id: [],
  comment_level: '',
  article_id: '',
  top_comment_id: '',
  second_comment_id: '',
  isPass: true
})

onMounted(() => {
  nextTick(() => {
    const tableComponent = document.getElementById('tabel-component')
    const paginationElement = document.getElementsByClassName('el-pagination')[0]

    if (tableComponent && paginationElement) {
      let height = tableComponent.offsetHeight
      let bottom = paginationElement.offsetHeight - 30
      data.value.height = height - bottom
    }

    if (searchBox.value && searchBox.value.searchData) {
      search(searchBox.value.searchData)
    }
  })
})

function onClose() {
  dialogShow.value = false
  disabledShow.value = false
  deleteAlertData.value.comment_id = []
}

function onOk() {
  deleteClickFun()
  dialogShow.value = false
}

// 历史记录
function handleHistory(val) {
  operationLog.value.message.auto_pk = val.auto_pk
  operationLog.value.message.id = val.id
  operationLog.value.message.content = val.content
  operationLog.value.getList()
  operationLog.value.drawer = true
}

// 高级标题跳转
function openLink(url) {
  window.open(url)
}

// 搜索接口
const search = val => {
  let obj
  // 如果是稿件标题
  if (val.comment_search_type == 'ARTICLE_TITLE') {
    obj = JSON.parse(JSON.stringify(val))
    obj.comment_search_type = 'ARTICLE_ID'
    if (searchBox.value.title_search_data) {
      obj.searchword = searchBox.value.title_search_data.slice(
        searchBox.value.title_search_data.indexOf('【') + 1,
        searchBox.value.title_search_data.indexOf('】')
      )
    }
  } else {
    obj = val
  }
  loading.value = true
  requests
    .list(obj)
    .then(res => {
      loading.value = false
      data.value.records = res.data.pg.records
      data.value.total = res.data.pg.total
      data.value.current = obj.current
    })
    .catch(error => {
      loading.value = false
      console.log(error)
    })
}

// 撤销至待审
const deleteClick2 = row => {
  deleteToPass.value = false
  requests.getDelType({ comment_id: row.comment_id || row.id }).then(msg => {
    if (msg.data.is_exist) {
      ElMessage({
        type: 'error',
        message: '该评论已被违规删除，不可撤销！'
      })
    } else {
      let commentId = row.comment_id || row.id
      if (!deleteAlertData.value.comment_id.includes(commentId)) {
        deleteAlertData.value.comment_id.push(commentId)
      }
      deleteAlertData.value.article_id = row.article_id
      deleteAlertData.value.top_comment_id = row.top_comment_id
      deleteAlertData.value.second_comment_id = row.second_comment_id
      deleteAlertData.value.comment_level = row.comment_level
      deleteAlertData.value.isPass = true
      requests
        .getParentCommentState({
          comment_level: row.comment_level,
          top_comment_id: row.top_comment_id,
          second_comment_id: row.second_comment_id,
          article_id: row.article_id,
          state: 'CANCEL_TO_PENDING'
        })
        .then(res => {
          if (res.code == 0) {
            // comment 存在 上级评论在伤处列表
            if (res.data.result.commentList.length > 0) {
              //   设置弹窗
              disableData.value = res.data.result.commentList
              res.data.result.commentList.forEach(v => {
                deleteAlertData.value.comment_id.push(v.id)
              })
              disabledShow.value = true
            } else {
              // 显示弹窗
              dialogShow.value = true
              disabledShow.value = false
            }
          } else {
            deleteAlertData.value.comment_id = []
            ElMessage({
              type: 'error',
              message: '请求失败！'
            })
          }
        })
        .catch(error => {
          deleteAlertData.value.comment_id = []
          console.log(error)
        })
    }
  })
}

// 撤销并通过
const deleteClick = row => {
  deleteToPass.value = true
  let commentId = row.comment_id || row.id
  if (!deleteAlertData.value.comment_id.includes(commentId)) {
    deleteAlertData.value.comment_id.push(commentId)
  }
  deleteAlertData.value.article_id = row.article_id
  deleteAlertData.value.top_comment_id = row.top_comment_id
  deleteAlertData.value.second_comment_id = row.second_comment_id
  deleteAlertData.value.comment_level = row.comment_level
  deleteAlertData.value.isPass = false
  requests
    .getParentCommentState({
      comment_level: row.comment_level,
      top_comment_id: row.top_comment_id,
      second_comment_id: row.second_comment_id,
      article_id: row.article_id,
      state: 'CANCEL_TO_PASS'
    })
    .then(res => {
      if (res.code == 0) {
        // comment 存在 上级评论在伤处列表
        if (res.data.result.commentList.length > 0) {
          //   设置弹窗
          disableData.value = res.data.result.commentList
          res.data.result.commentList.forEach(v => {
            deleteAlertData.value.comment_id.push(v.id)
          })
          disabledShow.value = true
        } else {
          // 显示弹窗
          dialogShow.value = true
          disabledShow.value = false
        }
      } else {
        deleteAlertData.value.comment_id = []
        ElMessage({
          type: 'error',
          message: '请求失败！'
        })
      }
    })
    .catch(error => {
      deleteAlertData.value.comment_id = []
      console.log(error)
    })
}

const deleteClickFun = () => {
  loading.value = true
  requests
    .batchAction({
      comment_ids: deleteAlertData.value.comment_id.join(','),
      status: deleteAlertData.value.isPass ? 'CANCEL_TO_PENDING' : 'CANCEL_TO_PASS',
      current_status: deleteAlertData.value.isPass ? 'PENDING' : 'DELETE',
      article_id: deleteAlertData.value.article_id,
      top_comment_id: deleteAlertData.value.top_comment_id,
      second_comment_id: deleteAlertData.value.second_comment_id,
      comment_level: deleteAlertData.value.comment_level
    })
    .then(res => {
      loading.value = false
      dialogShow.value = false
      if (res.code == 0) {
        deleteAlertData.value.comment_id = []
        ElMessage({
          type: 'success',
          message: '操作成功！'
        })
      } else {
        deleteAlertData.value.comment_id = []
        ElMessage({
          type: 'error',
          message: '撤销并通过失败！'
        })
      }
      search(searchBox.value.searchData)
    })
    .catch(error => {
      loading.value = false
    })
}

// 选择每页几条
const handleSizeChange = val => {
  searchBox.value.searchData.size = val
  searchBox.value.searchData.current = 1
  data.value.size = val
  data.value.current = 1
  search(searchBox.value.searchData)
}

// 点击分页器
const handleCurrentChange = val => {
  data.value.current = val
  searchBox.value.searchData.current = val
  search(searchBox.value.searchData)
}

// 显示评论人详情
const showUserDetail = row => {
  // 先显示基本信息
  userDetail.value = {
    nickname: row.comment_account,
    commentId: row.id,
    phone: '-',
    ipAddress: '-'
  }
  userDetailVisible.value = true

  // 调用接口获取详细信息
  getUserDetail(row.comment_user_id)
}

// 获取评论人详情接口
const getUserDetail = async id => {
  try {
    // 调用获取评论人详情接口
    const response = await requests.getUserDetail({ comment_user_id: id })

    userDetail.value = {
      nickname: response.data.comment.comment_user_name || userDetail.value.nickname,
      commentId: response.data.comment.id || userDetail.value.commentId,
      phone: response.data.comment.phone_number || '-',
      ipAddress: response.data.comment.extra ? JSON.parse(response.data.comment.extra).location || '-' : '-'
    }
  } catch (error) {
    console.error('获取评论人详情失败:', error)
    ElMessage({
      type: 'error',
      message: '获取评论人详情失败'
    })
  }
}
</script>

<style lang="scss" scoped>
.reply_info {
  font-size: 14px;
  line-height: 22px;
  background: #f5f5f5;
  border-radius: 6px;
  padding: 3px 5px;
  color: #666;
}

.replyHide {
  padding: 0 10px;
}

.reply {
  background: rgba(102, 177, 253, 0.23);
  border-radius: 2px;
  border: 1px solid #0b82fd;
  margin-right: 6px;
  font-size: 12px;
  color: #0b82fd;
  padding: 2px;
  font-family: 'Arial';
}

.reviewed {
  display: flex;
  align-items: center;
  width: 100%;
  cursor: pointer;
}

.elem:hover,
.reviewed:hover {
  color: rgba(11, 130, 253, 0.7);
}

.elem {
  cursor: pointer;
}

#tabel-component {
  height: 100%;

  :deep(.el-table) {
    height: 100%;

    .el-table__body-wrapper {
      overflow-y: auto;
    }
  }
}

.page {
  display: flex;
  justify-content: center;
  height: 45px;
}

.comment-user {
  color: #0b82fd;
  cursor: pointer;

  &:hover {
    color: rgba(11, 130, 253, 0.7);
    text-decoration: underline;
  }
}

.user-detail {
  .detail-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .label {
      width: 100px;
      font-weight: 500;
      color: #333;
      flex-shrink: 0;
    }

    .value {
      color: #666;
      word-break: break-all;
    }
  }
}
</style>
