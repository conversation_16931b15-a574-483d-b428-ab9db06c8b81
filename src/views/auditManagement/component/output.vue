<template>
  <div>
    <el-popover placement="bottom" :width="200" trigger="click">
      <template #reference>
        <el-button color="#0B82FD" type="primary" style="margin-left: 5px">
          导出
        </el-button>
      </template>
      <el-checkbox-group v-model="checkList">
        <el-checkbox label="评论人昵称、评论" :value="1"  disabled/>
        <el-checkbox label="稿件ID" :value="2" />
        <el-checkbox label="手机号" :value="3" />
      </el-checkbox-group>
      <div style="text-align: right; margin-top: 10px">
        <el-button color="#0B82FD" type="primary" size="small" @click="handleExport">
          下载
        </el-button>
      </div>
    </el-popover>
  </div>
</template>
<script setup>
import { Output } from '@/api/auditManagement/output'
import { ref } from 'vue'
const props = defineProps({
  type: {
    type: Number,
    default: 1
  },
  searchData: {
    type: Object,
    default: () => ({})
  }
})
const checkList = ref([1])

const handleExport = () => {
  let data = {}
  if(props.searchData.start_time) {
    data.start_time = props.searchData.start_time
  }
  if(props.searchData.end_time) {
    data.end_time = props.searchData.end_time
  }
  if(props.searchData.comment_type) {
    data.comment_type = props.searchData.comment_type
  }
  if(props.searchData.comment_search_type) {
    data.comment_search_type = props.searchData.comment_search_type
  }
  if(props.searchData.searchword) {
    data.searchword = props.searchData.searchword
  }
  if(props.searchData.category_id) {
    data.category_id = props.searchData.category_id
  }
  if(props.searchData.circle_id) {
    data.circle_id = props.searchData.circle_id
  }
  if(props.searchData.word_number_min) {
    data.word_number_min = props.searchData.word_number_min
  }
  if(props.searchData.word_number_max) {
    data.word_number_max = props.searchData.word_number_max
  }
  Output({
    state: props.type === 1 ? 'PENDING' : 'PASS',
    head: getNumberByCheckList(checkList.value),
    ...data
  }).then(res => {
    const a = document.createElement('a')
    a.href = window.URL.createObjectURL(res)
    a.download = `评论${props.type === 1 ? '待审核' : '已通过'}${new Date().toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' })}.xlsx`
    a.click()
  })
}
// 根据选中的复选框数组返回对应的数字
const getNumberByCheckList = (list) => {
  if (list.length === 1 && list.includes(1)) {
    return 0
  } else if (list.length === 2 && list.includes(1) && list.includes(2)) {
    return 1
  } else if (list.length === 2 && list.includes(1) && list.includes(3)) {
    return 2
  } else if (list.length === 3 && list.includes(1) && list.includes(2) && list.includes(3)) {
    return 3
  }
  return 0 // 默认返回0
}

</script>
<style lang="scss" scoped>

</style>

