<template>
  <div class="head-box">
    <div class="left"></div>
    <div class="right">
      <el-date-picker
        v-model="time"
        style="width: 220px; margin-right: 10px"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        :editable="false"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="defaultTime"
        :disabled-date="disabledDate"
        @change="handleTimeChange"
      >
      </el-date-picker>
      <!-- 全部频道 筛选-->
      <el-cascader
        v-model="cascadeValue"
        :options="options"
        :show-all-levels="false"
        clearable
        collapse-tags
        :props="optionsProps"
        placeholder="全部频道"
        style="margin-right: 10px; width: 130px"
        @change="change"
      />
      <!-- 举报类型 -->
      <el-select
        v-model="searchData.reporting_type"
        clearable
        placeholder="请选择举报类型"
        style="width: 110px; margin-right: 10px"
      >
        <el-option
          v-for="item of report_type"
          :key="item.id"
          :label="item.type_name"
          :value="item.id"
        >
        </el-option>
      </el-select>
      <el-input
        v-model="searchData.search_word"
        placeholder="请输入关键词"
        style="width: 200px"
        class="input-with-select"
        clearable
        @keyup.enter="handSearch"
      >
        <template #prepend>
          <el-select
            v-model="searchData.search_type"
            placeholder="请选择"
            style="width: 110px"
            @change="typeChange"
          >
            <el-option
              v-for="item of commentSearchType"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            >
            </el-option>
          </el-select>
        </template>
      </el-input>
      <!-- 搜索按钮 -->
      <el-button
        color="#0B82FD"
        type="primary"
        :icon="Search"
        style="margin-left: 5px"
        @click="handSearch"
      >
        搜索
      </el-button>
    </div>
  </div>
</template>
<script setup>
import { requests } from '@/api/auditManagement/reportReview.js'
import { Search } from '@element-plus/icons'
import { defineEmits, defineExpose, onMounted, ref } from 'vue'
import { ElMessage } from 'element-plus'

const emit = defineEmits(['search', 'ready'])
const cascadeValue = ref(null)
const options = ref([])
const optionsProps = ref({
  value: 'category_id',
  label: 'name'
})

const commentSearchType = ref([
  { key: 'CONTENT', label: '评论内容' },
  { key: 'ARTICLE_TITLE', label: '稿件标题' },
  { key: 'REPORT_PERSON', label: '举报人' }
])
const report_type = ref([])
const searchData = ref({
  category_id: '',
  search_type: 'CONTENT',
  search_word: '',
  reporting_type: ''
})
const time = ref([])
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]

onMounted(() => {
  requests.listCategoryTree().then(res => {
    let arr = res.data.category_list
    // for (let i = 0; i < arr.length; i++) {
    //   if (parseInt(arr[i].permission) == 0) {
    //     arr.splice(i--, 1)
    //   } else {
    //     for (let k = 0; k < arr[i].children.length; k++) {
    //       if (arr[i].children[k].permission == 0) {
    //         arr[i].children.splice(k--, 1)
    //       } else {
    //         for (let a = 0; a < arr[i].children[k].children.length; a++) {
    //           if (arr[i].children[k].children[a].permission == 0) {
    //             arr[i].children[k].children.splice(a--, 1)
    //           }
    //         }
    //       }
    //     }
    //   }
    // }
    options.value = arr
  })

  requests.getTypeList().then(res => {
    report_type.value = res.data.type_list
    emit('ready', res.data.type_list)
  })
})

const typeChange = () => {
  searchData.value.search_word = ''
}

const change = () => {
  if (cascadeValue.value) {
    let data = cascadeValue.value.join(',')
    data = data.split(',')
    data = Array.from(new Set(data))
    searchData.value.category_id = data[data.length - 1]
  } else {
    searchData.value.category_id = ''
  }
  handSearch()
}

const handSearch = () => {
  let searchObj = {}
  if (time.value) {
    searchObj.start_time = time.value[0]
    searchObj.end_time = time.value[1]
  } else {
    searchObj.start_time = ''
    searchObj.end_time = ''
  }
  searchObj.category_id = searchData.value.category_id
  searchObj.search_type = searchData.value.search_type
  searchObj.search_word = searchData.value.search_word
  searchObj.reporting_type = searchData.value.reporting_type
  emit('search', searchObj)
}

// 禁用日期函数 - 限制日期选择范围不超过30天
const disabledDate = date => {
  if (!time.value || time.value.length === 0) {
    return false
  }

  const [startTime, endTime] = time.value
  const thirtyDaysLater = new Date(new Date(startTime).getTime() + 30 * 24 * 60 * 60 * 1000)
  const thirtyDaysBefore = new Date(new Date(endTime).getTime() - 30 * 24 * 60 * 60 * 1000)

  if (startTime && !endTime) {
    return date.getTime() > thirtyDaysLater.getTime() || date.getTime() < startTime.getTime()
  }

  if (!startTime && endTime) {
    return date.getTime() < thirtyDaysBefore.getTime() || date.getTime() > endTime.getTime()
  }

  return false
}

// 时间变更处理函数
const handleTimeChange = val => {
  if (val && val.length === 2) {
    const [startTime, endTime] = val
    const startDate = new Date(startTime)
    const endDate = new Date(endTime)

    // 确保结束日期不早于开始日期
    if (endDate < startDate) {
      ElMessage.warning('结束时间不能早于开始时间')
      time.value = [startTime, startTime]
      return
    }

    // 检查是否超过30天
    const diffTime = Math.abs(endDate - startDate)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays > 30) {
      // 如果超过30天，将结束日期设置为开始日期后的第30天
      const maxEndDate = new Date(startDate)
      maxEndDate.setDate(startDate.getDate() + 30)
      const formattedEndDate = maxEndDate.toISOString().slice(0, 19).replace('T', ' ')

      ElMessage.warning('时间筛选范围不能超过30天，已自动调整为30天')
      time.value = [startTime, formattedEndDate]
    }
  }

  handSearch()
}

defineExpose({
  searchData
})
</script>
<style lang="scss" scoped>
.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;

  .left {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    white-space: nowrap;
    margin-right: 10px;
  }

  .right {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    row-gap: 6px;
  }
}
</style>
<style>
.input-with-select .el-input-group__append {
  background-color: #1890ff;
  color: #fff;
}
</style>
