<template>
  <el-drawer
    v-model="drawer"
    direction="rtl"
    title="添加马甲号"
    :size="620"
    close-on-click-moda="false"
  >
    <div class="search">
      <span>
        昵称
      </span>
      <el-input
        v-model="nick_name"
        style="width: 200px;margin:0 10px"
        placeholder="请输入昵称"
      />
      <el-button
        type="primary"
        style="width: 80px;"
        @click="getVest"
      >
        搜索
      </el-button>
    </div>
    <div class="number-box">
      <div v-for="item of list" :key="item.id" :class="item.id === act ? 'child act':'child'" @click="checkVest(item)">
        <img :src="item.image_url" />
        <div class="sub-title">
          <span>{{ item.nick_name }}</span>
          <p>{{ item.location.split(',')[1] + item.location.split(',')[2] }}</p>
        </div>
      </div>
    </div>
    <template #footer>
      <div style="flex: auto;">
        <el-button @click="drawer = false">取消</el-button>
        <el-button
          type="primary"
          @click="sureClick"
        >
          确认
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>
<script setup name="add">
import { ref } from 'vue'
import { requests } from '@/api/business/commentOpera'

const drawer = ref(false)
const nick_name = ref('')
const list = ref([])
const act = ref('')
function getVest() {
  requests.vest_list({ nick_name: nick_name.value, size: 20 }).then(res => {
    list.value = res.data.vest_list
  })
}
function checkVest(val) {
  act.value = val.id
}
// 确认
function sureClick() {
  if (act.value === '') {
    ElMessage.error('请选择要收藏的马甲号')
    return
  }
}
defineExpose({
  drawer
})
</script>
<style lang="scss" scoped>
.search {
  display: flex;
  align-items: center;
}
.number-box {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  .child.act{
    background-color: #deebfe;
  }
  .child {
    width: 135px;
    height: 60px;
    padding-top: 10px;
    margin: 0 10px 20px 0;
    background-color: #f2f2f2;
    border-radius: 5px;
    display: flex;
    position: relative;
    cursor: pointer;
    &:hover {
        border: 1px dashed #666;
    }
    img {
      width: 20px;
      height: 20px;
      margin-left: 5px;
      margin-right: 5px;
      border-radius: 50%;
      object-fit: cover;
      flex: 1;
    }
    .sub-title {
      width: 100px;
      span {
        display: block;
        height: 25px;
        width: 100px;
        line-height: 20px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 12px;
      }
      p {
        width: 100px;
        margin-left: -23px;
        color: rgb(11, 130, 253);
        font-size: 13px;
        margin-top: -5px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
