<template>
  <div>
    <el-drawer
      v-model="drawer"
      direction="rtl"
      title="参数编辑"
      :size="600"
      close-on-click-moda="false"
      @close="close"
      @open="open"
      @closed="closed"
    >
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        label-width="120px"
        class="demo-ruleForm"
        status-icon
        :size="500"
      >
        <h4>热门评论策略</h4>
        <el-form-item label-width="0px">
          自动推送热门评论：稿件评论点赞数达
          <el-input-number v-model="ruleForm.auto_like_count" :value-on-clear="0" :min="0" /> 次
        </el-form-item>
        <el-form-item label-width="0px">
          稿件热门评论数量限制:
          <el-input-number v-model="ruleForm.limit_top_count" :value-on-clear="0" :min="0" /> 条
        </el-form-item>
        <!-- <h4>删除重复评论策略</h4>
        <el-form-item label-width="0px">
          删除重复评论：同一用户同一稿件，
          <el-input-number v-model="ruleForm.repeat_second" :value-on-clear="10" :min="10" :max="1800" />
          秒内发布重复评论超过
          <el-input-number v-model="ruleForm.repeat_limit" :value-on-clear="0" :min="0" /> 条后，超出的重复评论自动删除。
        </el-form-item> -->
        <el-divider />
        <!-- <h4>评论自动化排序</h4>
        <el-form-item label-width="0px">
          潮客频道排序：<el-switch v-model="ruleForm.score_sort" />
        </el-form-item>
        <el-divider /> -->
      </el-form>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="close">取消</el-button>
          <el-button type="primary" @click="sureClick"> 确认 </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script setup name="add">
import { ref } from 'vue'
import { requests } from '@/api/system/parameter'
import { ElMessage } from 'element-plus'

// 传入的数据
const props = defineProps({
  editData: Object
})

const ruleFormRef = ref(null)
const ruleForm = ref({
  code: 'TOP',
  auto_like_count: '',
  limit_top_count: '',
  repeat_limit: '',
  score_sort: null // 潮客排序
})

const emit = defineEmits(['init'])
const drawer = ref(false)
const isEdit = ref(false)

onMounted(() => {
  //   console.log('传入的值', props.data)
})

const sureClick = () => {
  console.log('值', ruleForm.value)
  // 表单验证
  requests
    .update(ruleForm.value)
    .then(res => {
      console.log('res', res)
      ElMessage({
        message: '更改成功！',
        type: 'success'
      })
      drawer.value = false
      emit('init')
    })
    .catch(error => {
      console.log(error)
    })
}

// 打开弹窗时
const open = () => {
  console.log('打开弹窗')
  if (isEdit.value == true) {
    ruleForm.value = {
      code: 'TOP',
      ...props.editData
    }
  }
}

// 取消弹窗时
const close = formEl => {
  drawer.value = false
}

// 弹窗动画结束时
const closed = () => {
  isEdit.value = false
}

defineExpose({
  drawer,
  isEdit
})
</script>
<style lang="scss" scoped>
.avatar-uploader .el-upload {
  width: 120px;
  height: 120px;
  position: relative;
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  text-align: center;
  //   background: #ebebeb;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid #ebebeb;
}
</style>
