<template>
  <AbsoluteContainer>
    <page-main>

      <div class="box">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>热门评论策略</span>
            </div>
          </template>
          <el-button
          v-auth="'comment_config:update'"
          type="primary"
          style="width: 150px;margin-bottom: 20px;"
          @click="handleAdd"
        >
          参数配置
        </el-button>
          <div class="line">自动推送热门评论：稿件评论点赞数达【{{ data.auto_like_count }}】次</div>
          <div class="line">稿件热门评论数量限制：【{{ data.limit_top_count }}】条。</div>
        </el-card>
        <el-card
        v-auth="'reports:comment_reports'"
        class="box-card"
      >
        <template #header>
          <div class="card-header">
            <span>评论举报配置</span>
          </div>
        </template>
        <Report></Report>
      </el-card>

        <el-card
          v-auth="'vest:vest_sync'"
          class="box-card"
        >
          <template #header>
            <div class="card-header">
              <span>马甲号同步</span>
            </div>
          </template>
          <el-button
            v-auth="'vest:vest_sync'"
            type="primary"
            style="margin:-5px 0 10px 10px"
            @click="handleVestsync"
          >
            马甲号同步
          </el-button>
        </el-card>

      </div>
    </page-main>
    <Edit
      ref="edit"
      :edit-data="editData"
      @init="init"
    />
    <Add ref="add" @success="getVest" />
  </absolutecontainer>
</template>
<script setup name="parameter">
import Add from './modules/add.vue'
import Edit from './modules/edit.vue'
import Report from './modules/reportType.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  requests
} from '@/api/system/parameter'
import {
  ref
} from 'vue'

const edit = ref(null)
const editData = ref(null)
const data = ref({
  auto_like_count: '',
  limit_top_count: '',
  repeat_limit: '',
  is_auto_delete: null,
  repeat_second: '',
  replay_nickname: '小编',
  replay_portrait_url: null,
  location: null,
  show_location: null
})
const imgURL = ref('https://app-stc.zjol.com.cn/logo/cxw-logo.png')
onMounted(() => {
  init()
})
const add = ref(null)

function getVest() {
  // 保留函数以防有其他组件调用
}

function handleVestsync() {
  requests.vest_sync().then(res => {
    if (res.code == 0) {
      ElMessage({
        message: '同步成功',
        type: 'success'
      })
    } else {
      ElMessage({
        message: '同步失败',
        type: 'error'
      })
    }
  })
}
function handleAdd() {
  editData.value = data.value
  edit.value.isEdit = true
  edit.value.drawer = true
}

function init() {
  requests.detail({
    code: 'TOP'
  }).then(res => {
    data.value = res.data.detail
    data.value.replay_portrait_url = imgURL.value
  }).catch(res => {

  })
}

</script>
<style lang="scss" scoped>
.page-main {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}
.box {
  height: 100%;
  overflow-y: scroll;
}
.line {
  margin: 10px 0;
  span {
    float: left;
  }
}

.box-card {
  margin-bottom: 20px;
}
.userImg {
  width: 120px;
  height: 120px;
  border-radius: 50%;
}
.number-box {
  display: flex;
  flex-wrap: wrap;
  .child {
    width: 135px;
    height: 60px;
    padding-top: 10px;
    margin: 0 10px 20px 0;
    background-color: #f2f2f2;
    display: flex;
    position: relative;
    &:hover{
      .del{
        display: block;
      }
    }
    .del {
      display: none;
      position: absolute;
      top: 2px;
      right: 5px;
      cursor: pointer;
    }
    img {
      width: 20px;
      height: 20px;
      margin-left: 5px;
      margin-right: 5px;
      border-radius: 50%;
      object-fit: cover;
      flex: 1;
    }
    .sub-title {
      width: 100px;
      span {
        display: block;
        height: 25px;
        width: 100px;
        line-height: 20px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 12px;
      }
      p {
        width: 100px;
        margin-left: -23px;
        color: rgb(11, 130, 253);
        font-size: 13px;
        margin-top: -5px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
