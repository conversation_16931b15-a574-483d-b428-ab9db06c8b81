<template>
  <div>

    <!-- <Copyright v-if="$store.state.settings.showCopyright" /> -->
  </div>
</template>

<script setup name="Login">
import { ssoLogin } from '@/api/system/index'
const { proxy } = getCurrentInstance()
const store = useStore()
const route = useRoute(),
  router = useRouter()
import Cookies from 'js-cookie'
const title = import.meta.env.VITE_APP_TITLE
// 表单类型，login 登录，reset 重置密码

onMounted(() => {
  // router.push('/regularAudit')
  toSsoLogin()
})
async function toSsoLogin() {
  const access_token = route.query.access_token
  if (access_token) {
    // 请求SSO登录接口
    const data = await ssoLogin({ access_token: access_token })
    if (data.code === 0) {
      await store.dispatch('user/login')
      const redirectPath = route.query.redirect || '/regularAudit'
      // 登录成功后设置Cookie
      if (route.query.id) {
        // 携带id参数跳转
        router.push({
          path: redirectPath,
          query: { id: route.query.id }
        })
      } else {
        // 不携带id参数跳转
        console.log(Cookies.get('login'))
        router.replace(redirectPath)
      }
    }
  }
}

function showPassword() {
  passwordType.value = passwordType.value === 'password' ? '' : 'password'
  nextTick(() => {
    proxy.$refs.password.focus()
  })
}

function handleLogin() {
  proxy.$refs.loginFormRef.validate(valid => {
    if (valid) {
      loading.value = true
      store
        .dispatch('user/login', loginForm.value)
        .then(() => {
          loading.value = false
          if (loginForm.value.remember) {
            localStorage.setItem('login_account', loginForm.value.account)
          } else {
            localStorage.removeItem('login_account')
          }
          router.push(redirect.value)
        })
        .catch(() => {
          loading.value = false
        })
    }
  })
}

function handleFind() {
  proxy.$message({
    message: '重置密码仅提供界面演示，无实际功能，需开发者自行扩展',
    type: 'info'
  })
  proxy.$refs.resetFormRef.validate(valid => {
    if (valid) {
      // 这里编写业务代码
    }
  })
}

function testAccount(account) {
  loginForm.value.account = account
  loginForm.value.password = '123456'
  handleLogin()
}
</script>

<style lang="scss" scoped>
[data-mode='mobile'] {
  #login-box {
    max-width: 80%;
    .login-banner {
      display: none;
    }
  }
}
::v-deep input[type='password']::-ms-reveal {
  display: none;
}
.bg-banner {
  position: absolute;
  z-index: 0;
  width: 100%;
  height: 100%;
  background-image: url('../assets/images/login-bg.jpg');
  background-size: cover;
  background-position: center center;
}
#login-box {
  display: flex;
  justify-content: space-between;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  background: rgb(255 255 255 / 80%);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 0 5px #999;
  .login-banner {
    width: 300px;
    background-image: url('../assets/images/login-banner.jpg');
    background-size: cover;
    background-position: center center;
  }
  .login-form {
    width: 450px;
    padding: 50px 35px 30px;
    overflow: hidden;
    .title-container {
      position: relative;
      .title {
        font-size: 22px;
        color: #666;
        margin: 0 auto 30px;
        text-align: center;
        font-weight: bold;
        @include text-overflow;
      }
    }
  }
  ::v-deep .el-input {
    height: 48px;
    line-height: inherit;
    width: 100%;
    input {
      height: 48px;
    }
    .el-input__prefix,
    .el-input__suffix {
      display: flex;
      align-items: center;
    }
    .el-input__prefix {
      left: 10px;
    }
    .el-input__suffix {
      right: 10px;
    }
  }
  .flex-bar {
    display: flex;
    justify-content: space-between;
  }
  .el-checkbox {
    margin: 20px 0;
  }
}
.copyright {
  position: absolute;
  bottom: 30px;
  width: 100%;
  margin: 0;
  mix-blend-mode: difference;
}
</style>
