<template>
  <el-dialog
    v-model="dialogVisible"
    width="500px"
    :show-close="false"
    :custom-class="'column-settings-dialog'"
    @close="handleClose"
  >
    <template #header>
      <div class="custom-header">
        <span class="dialog-title">列设置</span>
        <CloseBold class="close-btn" @click="handleClose"></CloseBold>
      </div>
    </template>
    <div class="content-divider"></div>
    <div class="column-settings">
      <div class="right-content" v-loading="loading">
        <template v-if="!loading">
          <div class="options">
            <el-radio-group v-model="templateType" @change="handleTemplateTypeChange">
              <el-radio label="standard">标准模板</el-radio>
              <el-radio label="customize">自定义</el-radio>
            </el-radio-group>
          </div>
          <el-checkbox-group
            v-if="templateType === 'customize'"
            v-model="selectedColumns"
            class="column-list"
          >
            <el-checkbox
              v-for="column in columns"
              :key="column.id"
              :label="column"
              :disabled="column.cancelled === 1"
            >
              {{ column.display_name }}
            </el-checkbox>
          </el-checkbox-group>
          <div v-else class="standard-mode-message">标准模式即所有列全部展示。</div>
          <el-button type="primary" class="save-button" @click="saveSettings">保存</el-button>
        </template>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getColumnSettingsApi, saveColumnSettingsApi } from '@/api/components'
import { CloseBold } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

const props = defineProps({
  type: {
    type: String,
    required: true,
    validator: (value) => {
      return typeof value === 'string' && value.length > 0
    }
  }
})

const store = useStore()
const router = useRouter()
const dialogVisible = ref(true)
const templateType = ref('standard')
const loading = ref(true)
const columns = ref([])
const selectedColumns = ref([])
const isInitialLoad = ref(true)
const emit = defineEmits(['close'])

const handleClose = () => {
  dialogVisible.value = false
  emit('close')
}

const fetchColumnSettings = async () => {
  loading.value = true
  try {
    const res = await getColumnSettingsApi(props.type)
    if (res && res.data) {
      if (isInitialLoad.value) {
        templateType.value = res.data.mode || 'standard'
        isInitialLoad.value = false
      }
      if (res.data.list && templateType.value === 'customize') {
        columns.value = res.data.list
        selectedColumns.value = columns.value.filter(column => column.is_visible === '1')
      } else {
        columns.value = []
        selectedColumns.value = []
      }
    }
  } catch (error) {
    console.error('获取列配置失败:', error)
  } finally {
    loading.value = false
  }
}

const handleTemplateTypeChange = newType => {
  templateType.value = newType
  if (newType === 'standard') {
    columns.value = []
    selectedColumns.value = []
  } else if (newType === 'customize' && columns.value.length === 0) {
    fetchColumnSettings()
  }
}

onMounted(() => {
  fetchColumnSettings()
})

const saveSettings = async () => {
  try {
    const data = {
      menu_tab: props.type,
      ids: templateType.value === 'standard' ? '0' : selectedColumns.value.map(column => column.id).join(','),
      mode: templateType.value
    }
    await saveColumnSettingsApi(data)
    handleClose()
    router.go(0)
  } catch (error) {
    console.error('保存列配置失败:', error)
  }
}
</script>

<style scoped lang="scss">
.custom-header {
  margin-bottom: 2px;
  padding-bottom: 2px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .dialog-title {
    font-size: 16px;
    font-weight: 600;
    color: #666666;
  }

  .close-btn {
    width: 14px;
    height: 14px;
    background: none;
    border: none;
    color: #333333;
    cursor: pointer;
  }
}

.content-divider {
  width: 100%;
  height: 0px;
  background-color: #e6e6e6;
}

.column-settings {
  display: flex;
  height: 500px;
}

.right-content {
  position: relative;
  flex: 1;
  border: 1px solid #f4f4f4;
  padding: 15px;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

.options {
  margin-bottom: 20px;
}

.column-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: auto;
  flex-grow: 1;
}

.save-button {
  margin-top: 20px;
  width: 120px;
  align-self: center;
}

.standard-mode-message {
  text-align: center;
  color: #909399;
  align-self: flex-start;
}

::v-deep .column-settings-dialog {
  border-radius: 8px;
  overflow: hidden;

  .el-dialog__header {
    padding-bottom: 8px !important;
  }
}
</style>
