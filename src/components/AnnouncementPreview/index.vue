<script setup>
import { Editor } from '@wangeditor/editor-for-vue'
import { computed, ref } from 'vue'
import '@wangeditor/editor/dist/css/style.css'

const mode = ref('default')
const editorConfig = ref({ readOnly: true })
const store = useStore()
const valueHtml = computed(() => store.state.announcement.htmlContent)
const announcementTitle = computed(() => store.state.announcement.announcementTitle)
const author = computed(() => store.state.announcement.author)
const time = computed(() => store.state.announcement.time)
</script>

<template>
  <div class="page-container">
    <div class="top-container">
      <div class="title">{{ announcementTitle }}</div>
      <div class="line-time">
        <div class="author">发布人：{{ author }}</div>
        <div class="time">发布时间：{{ time }}</div>
      </div>
    </div>
    <Editor v-model="valueHtml" :default-config="editorConfig" :mode="mode" />
  </div>
</template>

<style scoped lang="scss">
::v-deep .w-e-text-container [data-slate-editor] {
  padding: 0;
}
.top-container {
  display: flex;
  flex-direction: column;
  justify-content: start;
  width: 100%;
}
.title {
  font-weight: bold;
  font-size: 16px;
  color: #333333;
}
.time {
  font-size: 14px;
  color: #333333;
}
.author {
  font-size: 14px;
  color: #333333;
  margin-right: 30px;
}
.page-container {
  max-height: 70vh;
  overflow-y: scroll;
}
.line-time {
  display: flex;
  flex-direction: row;
  margin-top: 10px;
}
</style>
