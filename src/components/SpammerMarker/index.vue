<template>
  <el-popover
    effect="dark"
    :disabled="isDisabled"
    placement="top"
    :width="160"
    trigger="hover"
  >
    <template #reference>
      <span class="user-name">
        <slot name="reference"></slot>
      </span>
    </template>
    <template #default>
      <div class="mark-spammer-container">
        <img src="@/assets/images/icon_ding_spammer.png" alt="spammer" class="spammer-icon" />
        <span class="mark-spammer" @click="onClickMarkSpammer">标记为灌水用户</span>
      </div>
    </template>
  </el-popover>
  <!-- <teleport to="body">
    <el-dialog v-model="dialogVisible" title="确认操作" width="30%">
      <span>确定要将用户 {{ currentUser }} 标记为灌水用户吗？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleConfirmMarkAsSpammer">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </teleport> -->
</template>

<script setup>
import { useSpammerMarker } from '@/hooks/useSpammerMarker'
import { useStore } from 'vuex'
import { computed } from 'vue'

const store = useStore()

const props = defineProps({
  spammer: {
    type: Boolean,
    required: true
  },
  userData: {
    type: Object,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const checkPermission = permission => {
  return store.state.menu.permissions.includes(permission)
}

const emit = defineEmits(['update:spammer'])
const hasSpammerHandlePermission = checkPermission('spammer_user:mark')

const isDisabled = computed(() => {
    if (props.disabled) {
        return true
    }
    if (!hasSpammerHandlePermission) {
        return true
    }
    if (props.spammer) {
        return true
    }
    return false
})

const updateSpammerStatus = success => {
  if (success) {
    emit('update:spammer', true)
  }
}

const { dialogVisible, currentUser, showConfirmDialog, confirmMarkAsSpammer, closeDialog } =
  useSpammerMarker()

const handleConfirmMarkAsSpammer = () => {
  confirmMarkAsSpammer(updateSpammerStatus)
}

const onClickMarkSpammer = () => {
  showConfirmDialog(props.userData)
  handleConfirmMarkAsSpammer()
}
</script>
<style lang="scss" scoped>
.user-name {
  cursor: pointer;
  color: inherit;

  &:hover {
    color: #0b82fd;
  }
}

.mark-spammer-container {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }

  .spammer-icon {
    width: 16px;
    height: 16px;
  }
}

.mark-spammer {
  color: #fff;
  cursor: pointer;
}
</style>
