import api from '../index.js'
const requests = {
  // 申诉审核列表
  reportsList(data) {
    return api({
      url: 'reports/reports_list',
      method: 'get',
      params: data
    })
  },
  // 批量审核
  batchActionAudit(data) {
    return api({
      url: 'comment/batch_action_audit',
      method: 'post',
      data
    })
  },
  // 待审核列表
  list(data) {
    return api({
      url: 'comment/list',
      method: 'get',
      params: data
    })
  },
  //  批量通过
  batchAction(data) {
    return api({
      url: 'comment/batch_action',
      method: 'post',
      data
    })
  },
  // 稿件标题 模糊搜索
  searchList(data) {
    return api({
      url: 'comment/search_list_by_article_title',
      method: 'get',
      params: data
    })
  },
  // 判断上级评论是否已删除接口
  getParentCommentState(data) {
    return api({
      url: 'comment/get_parent_comment_state',
      method: 'get',
      params: data
    })
  },
  //   已删除弹窗级联的二级和三级评论列表接口
  getSubComments(data) {
    return api({
      url: 'comment/get_sub_comments',
      method: 'get',
      params: data
    })
  },
  // 频道列表
  listCategoryTree(data) {
    return api({
      url: 'category/list_category_tree',
      method: 'get',
      params: data
    })
  },
  //  敏感词效验
  checkSensitiveWord(data) {
    return api({
      url: 'risk/comment/check_sensitive_word',
      method: 'get',
      params: data
    })
  },
  //  小编回复
  editorReplay(data) {
    return api({
      url: 'comment/editor_replay',
      method: 'post',
      data
    })
  },
  // 获取圈子列表
  getcircleList(data) {
    return api({
      url: 'circle/list',
      method: 'get',
      params: data
    })
  },
  // 频道分组列表
  getGroupListApi(data = {}) {
    return api({
      url: 'category_group/list',
      method: 'get',
      params: {
        ...data,
        desc_or_asc: 'desc'
      }
    })
  },
  // 判断是否能被撤销至待审核
  getDelType(data) {
    return api({
      url: 'reports/reports_exist',
      method: 'get',
      params: data
    })
  },
  // 判断上级评论是否已通过接口
  getParentCommentState2(data) {
    return api({
      url: 'comment/pending_parent_comment',
      method: 'get',
      params: data
    })
  },
  // 获取评论用户列表
  getCommentUserList(data) {
    return api({
      url: 'comment_user_info/list',
      method: 'get',
      params: data
    })
  },
  // 获取评论用户详情
  getCommentUserDetail(data) {
    return api({
      url: 'comment_user_info/detail',
      method: 'get',
      params: data
    })
  },

  // 马甲号列表
  vest_list(data) {
    return api({
      url: 'vest/vest_list',
      method: 'get',
      params: data
    })
  },
  // 已收藏马甲号列表
  vest_collect_list(data) {
    return api({
      url: 'vest_collect/vest_collect_list',
      method: 'get',
      params: data
    })
  },
  // 添加马甲号
  addVestCollect(data) {
    return api({
      url: 'vest_collect/vest_collect_add',
      method: 'get',
      params: data
    })
  },
  // 设为热评
  setHot(data) {
    return api({
      url: 'circle_management/comment/on_off_hot',
      method: 'post',
      data
    })
  },
  // 获取评论人详情
  getUserDetail(data) {
    return api({
      url: 'comment/user_info',
      method: 'get',
      params: data
    })
  },
}
export { requests }
