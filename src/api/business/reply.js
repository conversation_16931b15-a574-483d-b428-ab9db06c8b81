import api from '../index.js'
const requests = {
   // 后台评论回复
   vest_comment_reply(data) {
    return api({
      url: 'comment_operation/comments/vest_comment_reply',
      method: 'post',
      data
    })
  },
  // 马甲号列表
  vest_list(data) {
    return api({
      url: 'vest/vest_list',
      method: 'get',
      params: data
    })
  },
  // 已收藏马甲号列表
  vest_collect_list(data) {
    return api({
      url: 'vest_collect/vest_collect_list',
      method: 'get',
      params: data
    })
  },
  // 添加马甲号
  addVestCollect(data) {
    return api({
      url: 'vest_collect/vest_collect_add',
      method: 'get',
      params: data
    })
  },
  // 删除马甲号
  deleteVestCollect(data) {
    return api({
      url: 'vest_collect/vest_collect_delete',
      method: 'get',
      params: data
    })
  },
   //  敏感词效验
   checkSensitiveWord(data) {
    return api({
      url: 'risk/comment/check_sensitive_word',
      method: 'get',
      params: data
    })
  },
}
export { requests }
