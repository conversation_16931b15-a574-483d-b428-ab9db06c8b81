const Layout = () => import('@/layout/index.vue')
export default {
  path: '/auditManagement',
  redirect: '/auditManagement/regularAudit',
  name: 'auditManagement',
  meta: {
    title: '审核管理',
    icon: 'sidebar-examine'
  },

  children: [
    {
      path: '/regularAudit',
      name: 'regularAudit',
      component: () => import('@/views/auditManagement/regularAudit/index.vue'),
      meta: {
        title: '常规审核'
      }
    },
    {
      path: '/reportReview',
      name: 'reportReview',
      component: () => import('@/views/auditManagement/reportReview/index.vue'),
      meta: {
        title: '举报审核'
      }
    },
    {
      path: '/appealAudit',
      name: 'appealAudit',
      component: () => import('@/views/auditManagement/appealAudit/index.vue'),
      meta: {
        title: '申诉审核'
      }
    }
  ]
}
