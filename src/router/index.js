import store from '@/store'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css' // progress bar style
import { createRouter, createWebHashHistory } from 'vue-router'
import auditManagement from './modules/auditManagement'
import system from './modules/system'
// 固定路由
const constantRoutes = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login.vue'),
    meta: {
      title: '登录'
    }
  },
  {
    path: '/debugLogin',
    name: 'debuglogin',
    component: () => import('@/views/debuglogin.vue'),
    meta: {
      title: '登录'
    }
  },
  {
    path: '/',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: '/dashboard',
        name: 'dashboard',
        component: () => import('@/views/index.vue'),
        meta: {
          title: '控制台',
          default: true
        }
      },
      {
        path: '/personal/setting',
        name: 'personalSetting',
        component: () => import('@/views/personal/setting.vue'),
        meta: {
          title: '个人设置',
          cache: 'personalEditPassword',
          breadcrumbNeste: [{ title: '个人设置', path: '/personal/setting' }]
        }
      },
      {
        path: '/personal/edit/password',
        name: 'personalEditPassword',
        component: () => import('@/views/personal/edit.password.vue'),
        meta: {
          title: '修改密码',
          breadcrumbNeste: [{ title: '修改密码', path: '/personal/edit/password' }]
        }
      },
      {
        path: '/reload',
        name: 'reload',
        component: () => import('@/views/reload.vue')
      },
    ]
  }
]

// 动态路由（异步路由、导航栏路由）
const asyncRoutes = [auditManagement, system]

const lastRoute = {
  path: '/:pathMatch(.*)*',
  component: () => import('@/views/404.vue'),
  meta: {
    title: '找不到页面'
  }
}

// 提前将所有路由合并，包括404路由
const allRoutes = [...constantRoutes, ...asyncRoutes, lastRoute]

const router = createRouter({
  history: createWebHashHistory(),
  routes: allRoutes // 直接挂载所有路由
})

// 初始化store中的路由状态
function initStoreRoutes() {
  // 设置路由已生成标志
  store.commit('menu/setRoutes', asyncRoutes)
  store.commit('menu/setDefaultOpenedPaths', asyncRoutes)
  // 预先获取表格权限
  store.dispatch('menu/getTalePermissions')
}

router.beforeEach(async (to, from, next) => {
  // 开启进度条
  store.state.settings.enableProgress && NProgress.start()
  // 判断用户是否已登录
  if (store.getters['user/isLogin']) {
    // 非single模式下，根据path定位主导航的选中状态
    if (store.state.settings.menuMode !== 'single') {
      store.commit('menu/setHeaderActived', to.path)
    }

    // 如果store中的路由未初始化，则初始化
    if (!store.state.menu.isGenerate) {
      // 获取用户权限信息，但不再用于生成路由
      await store.dispatch('menu/generateRoutesAtFront', asyncRoutes)
      // 将全部路由设置到store中
      store.commit('menu/setIsGenerate', true)
    }

    // 已登录状态下进入登录页的处理
    if ((to.name === 'login' || to.name === 'debuglogin') && !to.query.id) {
      return next({
        path: '/regularAudit',
        replace: true
      })
    }

    // 控制台页面处理
    if (!store.state.settings.enableDashboard && to.name === 'dashboard') {
      // 未开启控制台页面时，进入侧边栏第一个模块
      if (store.getters['menu/sidebarRoutes'].length > 0) {
        return next({
          path: '/regularAudit',
          replace: true
        })
      }
    }

    // 正常路由放行
    next()
  } else {
    // 未登录状态的路由拦截
    if (to.name !== 'login' && to.name !== 'debuglogin') {
      // 构建查询参数，保留access_token和id参数
      const query = { redirect: to.fullPath }

      // 如果路由中包含access_token或id参数，则携带这些参数
      if (to.query.access_token) {
        query.access_token = to.query.access_token
      }
      if (to.query.id) {
        query.id = to.query.id
      }

      // 重定向到登录页，并记录原目标路径和相关参数
      next({
        name: 'login',
        query: query
      })
    } else {
      next()
    }
  }
})

router.afterEach((to, from) => {
  store.state.settings.enableProgress && NProgress.done()
  // 设置页面 title
  to.meta.title &&
    store.commit(
      'settings/setTitle',
      typeof to.meta.title === 'function' ? to.meta.title() : to.meta.title
    )
})

export default router
