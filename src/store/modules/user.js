import api from '@/api'
import { logout } from '@/api/system/index'
import { useStore } from 'vuex'
const store = useStore()
const route = useRoute()
const state = () => ({
  account: localStorage.account || '',
  token: localStorage.token || '',
  failure_time: localStorage.failure_time || '',
  permissions: [],
  login: false
})

const getters = {
  isLogin: state => {
    return state.login
  }
}

const actions = {
  login({ commit,state }, data) {
    return new Promise((resolve,reject)=>{
      state.login = true
      resolve()
    })

  },
  logout({ commit,state }) {
    commit('removeUserData')
    commit('menu/invalidRoutes', null, { root: true })
    commit('menu/removeRoutes', null, { root: true })
    logout({}).then(res => {
      state.login = false
      // 清除已读消息记录
      window.location.reload()
    })

  },
  // 获取我的权限
  getPermissions({ state, commit }) {
    return new Promise(resolve => {
      // 通过 mock 获取权限
      let permissions = [
        'permission.browse',
        'permission.create',
        'permission.edit',
        'permission.remove'
      ]
      commit('setPermissions', permissions)
      resolve(permissions)
      //   api.get('member/permission', {
      //     baseURL: '/mock/',
      //     params: {
      //       account: state.account
      //     }
      //   }).then(res => {

    //   })
    })
  },
  editPassword({ state }, data) {
    return new Promise(resolve => {
      api.post(
        'member/edit/password',
        {
          account: state.account,
          password: data.password,
          newpassword: data.newpassword
        },
        {
          baseURL: '/mock/'
        }
      ).then(() => {
        resolve()
      })
    })
  }
}

const mutations = {
  setUserData(state, data) {
    localStorage.setItem('account', data.account)
    localStorage.setItem('token', data.token)
    localStorage.setItem('failure_time', data.failure_time)
    state.account = data.account
    state.token = data.token
    state.failure_time = data.failure_time
  },
  removeUserData(state) {
    localStorage.removeItem('account')
    localStorage.removeItem('token')
    localStorage.removeItem('failure_time')
    state.account = ''
    state.token = ''
    state.failure_time = ''
  },
  setPermissions(state, permissions) {
    state.permissions = permissions
  }
}

export default {
  namespaced: true,
  state,
  actions,
  getters,
  mutations
}
