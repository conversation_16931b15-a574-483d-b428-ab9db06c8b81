import path from 'path-browserify'
import { deepClone } from '@/util'
import { getJurisdiction, getTableJurisdiction } from '@/api/system/index'

function hasPermission(permissions, route) {
  let isAuth = false
  if (route.meta && route.meta.auth) {
    isAuth = permissions.some(auth => {
      if (typeof route.meta.auth == 'string') {
        return route.meta.auth === auth
      } else {
        return route.meta.auth.some(routeAuth => {
          return routeAuth === auth
        })
      }
    })
  } else {
    isAuth = true
  }
  return isAuth
}

function filterAsyncRoutes(routes, permissions) {
  const res = []
  routes.forEach(route => {
    let tmpRoute = deepClone(route)
    if (hasPermission(permissions, tmpRoute)) {
      if (tmpRoute.children) {
        tmpRoute.children = filterAsyncRoutes(tmpRoute.children, permissions)
        tmpRoute.children.length && res.push(tmpRoute)
      } else {
        res.push(tmpRoute)
      }
    }
  })
  return res
}




const state = () => ({
  isGenerate: false,
  routes: [],
  defaultOpenedPaths: [],
  headerActived: 0,
  currentRemoveRoutes: [],
  user: {},
  permissions: [],
  tablePermissions: []
})

const getters = {
  // 由于 getter 的结果不会被缓存，导致导航栏切换时有明显的延迟，该问题会在 Vue 3.2 版本中修复，详看 https://github.com/vuejs/vuex/pull/1883
  routes: (state, getters, rootState) => {
    let routes
    if (rootState.settings.menuMode === 'single') {
      routes = [{ children: [] }]
      state.routes.map(item => {
        routes[0].children.push(...item.children)
      })
    } else {
      routes = state.routes
    }
    return routes
  },
  sidebarRoutes: (state, getters) => {
    return getters.routes.length > 0 ? getters.routes : []
  },
  sidebarRoutesFirstDeepestPath: (state, getters) => {
    return '/'
  },
  user: (state, getters, rootState) => {
    return state.user
  },
  permissions: (state, getters, rootState) => {
    return state.permissions
  },
  tablePermissions: (state, getters, rootState) => {
    return state.tablePermissions
  }
}

const actions = {
  // 根据权限动态生成路由（前端生成）
  generateRoutesAtFront({ rootState, dispatch, commit }, asyncRoutes) {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async resolve => {
      getJurisdiction().then(res => {
        commit('setMsg', res)
        let accessedRoutes
        accessedRoutes = deepClone(asyncRoutes)
        accessedRoutes = deepClone(asyncRoutes)
        commit('setRoutes', accessedRoutes)

        // 将三级及以上路由数据拍平成二级

        commit('setDefaultOpenedPaths', asyncRoutes)
        resolve(asyncRoutes)
        //   将三级及以上路由数据拍平成二级+
      })
    })
  },
  // 获取列表展示权限
  getTalePermissions({ rootState, dispatch, commit }) {
    getTableJurisdiction().then(res => {
      commit('setTablePermissions', res)
    })
  },
  // 生成路由（后端获取）
  generateRoutesAtBack({ rootState, dispatch, commit }) {
    return new Promise(resolve => {
      //   api.get('route/list', {
      //     baseURL: '/mock/'
      //   }).then(async res => {

      getTableJurisdiction().then(res => {
        commit('setTablePermissions', res)
      })
    })
    // })
  }
}

const mutations = {
  setMsg: (state, value) => {
    state.user = value.data.admin
    state.permissions = value.data.permissions
  },
  setTablePermissions: (state, value) => {
    state.tablePermissions = value.data.list
  },
  invalidRoutes(state) {
    state.isGenerate = false
    state.routes = []
    state.defaultOpenedPaths = []
    state.headerActived = 0
  },
  setRoutes(state, routes) {
    state.isGenerate = true
    let newRoutes = deepClone(routes)
    state.routes = newRoutes.filter(item => {
      return item.children.length != 0
    })
  },
  setIsGenerate(state, value) {
    state.isGenerate = value
  },
  setDefaultOpenedPaths(state, routes) {
    let defaultOpenedPaths = []

    routes.map((item, index) => {
      !item.meta.defaultOpened && defaultOpenedPaths.push(item.path)
      item.children &&
        item.children.map(child => {
          child.meta.defaultOpened && defaultOpenedPaths.push(path.resolve(item.path, child.path))
        })
    })
    state.defaultOpenedPaths = defaultOpenedPaths
  },
  // 根据路由判断属于哪个头部导航
  setHeaderActived(state, path) {
    state.routes.map((item, index) => {
      if (
        item.children.some(r => {
          return path.indexOf(r.path + '/') === 0 || path == r.path
        })
      ) {
        state.headerActived = index
      }
    })
  },
  // 切换头部导航
  switchHeaderActived(state, index) {
    state.headerActived = index
  },
  // 记录 accessRoutes 路由，用于登出时删除路由
  setCurrentRemoveRoutes(state, routes) {
    state.currentRemoveRoutes = routes
  },
  // 清空动态路由
  removeRoutes(state) {
    state.currentRemoveRoutes.forEach(removeRoute => {
      removeRoute()
    })
    state.currentRemoveRoutes = []
  }
}

export default {
  namespaced: true,
  state,
  actions,
  getters,
  mutations
}
