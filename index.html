<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><%= title %></title>
  <style>
    #app {
      height: 100%;
    }

    .fantastic-admin-home {
      position: absolute;
      z-index: 10000;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      user-select: none;
      color: #736477;
      background-color: snow;
    }

    .fantastic-admin-home .loading {
      height: 40px;
      width: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
    }

    .fantastic-admin-home .square {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 20px;
      width: 20px;
    }

    .fantastic-admin-home .square::before {
      content: "";
      width: 10px;
      height: 10px;
      border-radius: 15%;
      border: 3px solid #8c858f;
    }

    .fantastic-admin-home .loading.animate .square::before {
      animation: square-to-dot-animation 1.2s linear;
    }

    .fantastic-admin-home .loading.animate .square:nth-child(1)::before {
      animation-delay: calc(100ms * 1);
    }

    .fantastic-admin-home .loading.animate .square:nth-child(2)::before {
      animation-delay: calc(100ms * 2);
    }

    .fantastic-admin-home .loading.animate .square:nth-child(3)::before {
      animation-delay: calc(100ms * 3);
    }

    .fantastic-admin-home .loading.animate .square:nth-child(4)::before {
      animation-delay: calc(100ms * 4);
    }

    @keyframes square-to-dot-animation {

      15%,
      55% {
        border-radius: 100%;
        width: 0;
        height: 0;
        margin: 5px;
        border-width: 5px;
      }

      70% {
        border-radius: 15%;
        width: 10px;
        height: 10px;
        margin: initial;
        border-width: 3px;
      }
    }

    .fantastic-admin-home .text {
      font-size: 24px;
      margin-top: 20px;
    }

    p {
      margin-top: 0;
      margin-bottom: 0;
    }

  </style>
  <%- copyrightScript %> <% if (appMode == 'example') { %>
  <script>
    var _hmt = _hmt || [];
    _hmt.push([
      "_requirePlugin",
      "UrlChangeTracker",
      {
        shouldTrackUrlChange: function (newPath, oldPath) {
          return newPath && oldPath;
        },
      },
    ]);
    (function () {
      var hm = document.createElement("script");
      if (location.origin.includes("gitee")) {
        hm.src =
          "https://hm.baidu.com/hm.js?ba0ee7b31f404b7dc10bfcd8bdc7183d";
      } else {
        hm.src =
          "https://hm.baidu.com/hm.js?d07a64f7a02cd5e9c01e1b36948ac4a5";
      }
      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(hm, s);
    })();

  </script>
  <% } %>
</head>

<body>
  <div id="app">
    <div class="fantastic-admin-home">
      <!-- <div class="loading">
        <div class="square"></div>
        <div class="square"></div>
        <div class="square"></div>
        <div class="square"></div>
      </div> -->
      <!-- <div class="text"><%= title %>载入中…</div> -->
    </div>
    <script>
      // const Loading = document.querySelector(".loading");
      // Loading.classList.add("animate");
      // Loading.addEventListener("animationend", function () {
      //   setTimeout(function () {
      //     Loading.classList.remove("animate");
      //   }, 600);
      //   setTimeout(function () {
      //     Loading.classList.add("animate");
      //   }, 1000);
      // });

    </script>
  </div>
  <script type="module" src="/src/main.js"></script>
  <% if (debugTool == 'eruda') { %>
  <script src="//cdn.jsdelivr.net/npm/eruda/eruda.min.js"></script>
  <script>
    eruda.init();

  </script>
  <% } %> <% if (debugTool == 'vconsole') { %>
  <script src="//cdn.jsdelivr.net/npm/vconsole/dist/vconsole.min.js"></script>
  <script>
    new VConsole();

  </script>
  <% } %>
</body>

</html>
