NODE_ENV = production

# 页面标题
VITE_APP_TITLE = 读嘉智慧化互动管理平台
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_APP_API_BASEURL = 'https://test-comment.jiaxingren.com/endpoint/'
# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VITE_APP_DEBUG_TOOL =

# 是否在打包时生成 sourcemap
VITE_BUILD_SOURCEMAP = false
# 是否在打包时删除 console 代码
VITE_BUILD_DROP_CONSOLE = true
# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS =
# 是否在打包时候生成PWA
VITE_BUILD_PWA = false

VITE_OPEN_PROXY = true
